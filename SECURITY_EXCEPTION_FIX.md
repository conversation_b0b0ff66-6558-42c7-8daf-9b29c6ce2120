# Security Exception Çözümü

## 🔒 **SUNUCU GÜVENLİK HATASI ÇÖZÜLDİ**

### ❌ **Hata:**
```
SecurityException: Request for the permission of type 'System.Security.Permissions.SecurityPermission' failed.
```

### ✅ **<PERSON><PERSON><PERSON><PERSON><PERSON>:**

## 1. **FULL TRUST AYARI (ÖNERİLEN)**

### **Web.config'e Eklendi:**
```xml
<system.web>
  <trust level="Full" />
</system.web>
```

### **Sunucu Yöneticisine Söyleyin:**
- **IIS Manager** > **Trust Levels** > **Full** seçin
- Veya **machine.config**'te Full Trust ayarlayın

## 2. **MEDIUM TRUST UYUMLU KOD (YEDEK ÇÖZÜM)**

### **SafeUrlHelper Sınıfı Oluşturuldu:**
- `YasinKaratas.Web/Helpers/SafeUrlHelper.cs`
- Medium Trust'ta güvenlik hatası alırsa manuel URL oluşturur
- Tüm `Url.Action` çağrıları değiştirildi

### **Kullanım:**
```csharp
// Eski (güvenlik hatası verebilir)
@Url.Action("Index", "Home")

// Yeni (güvenli)
@SafeUrlHelper.HomeUrl()
```

## 3. **DEPLOYMENT ADIMLARI**

### **A) Dosyaları Kopyalayın:**
```
YasinKaratas.Web/
├── bin/                    ← Tüm DLL'ler
├── Views/                  ← Güncellenmiş view'lar
├── Helpers/                ← SafeUrlHelper.cs
├── Web.config              ← Trust level eklendi
└── ...
```

### **B) IIS Ayarları:**
1. **Application Pool**: .NET Framework v4.0
2. **Trust Level**: Full (önemli!)
3. **Managed Pipeline**: Integrated

### **C) Web.config Kontrol:**
```xml
<system.web>
  <trust level="Full" />
  <compilation debug="false" targetFramework="4.8" />
</system.web>
```

## 4. **TEST ADIMLARI**

### **1. Trust Level Test:**
- Siteyi açın: `https://yasinkaratas.com.tr`
- Eğer hala SecurityException alıyorsanız → Medium Trust aktif

### **2. URL Test:**
- Ana sayfa linklerine tıklayın
- Menü navigasyonu test edin
- Dil değiştirme test edin

### **3. Hata Durumunda:**
```
1. Web.config'te <trust level="Full" /> var mı?
2. IIS'te Trust Level Full mu?
3. Application Pool doğru mu?
4. Bin klasöründe tüm DLL'ler var mı?
```

## 5. **HOSTING SAĞLAYICI AYARLARI**

### **Shared Hosting:**
- Çoğu shared hosting Medium Trust kullanır
- **SafeUrlHelper** otomatik devreye girer
- Ek ayar gerekmez

### **VPS/Dedicated:**
- Full Trust ayarlayabilirsiniz
- **machine.config** erişimi var
- Tam kontrol

### **Cloud Hosting (Azure, AWS):**
- Genelde Full Trust destekler
- Web.config ayarı yeterli

## 6. **SORUN GİDERME**

### **Hala SecurityException Alıyorsanız:**

#### **A) Debug Modunu Açın:**
```xml
<system.web>
  <compilation debug="true" />
  <customErrors mode="Off" />
</system.web>
```

#### **B) Detaylı Hata Mesajını Kontrol Edin:**
- Hangi satırda hata oluyor?
- Hangi permission eksik?

#### **C) Event Viewer Kontrol Edin:**
- Windows Logs > Application
- ASP.NET hatalarını arayın

## 7. **PRODUCTION AYARLARI**

### **Güvenlik İçin:**
```xml
<system.web>
  <trust level="Full" />
  <compilation debug="false" />
  <customErrors mode="On" />
  <httpCookies requireSSL="true" />
</system.web>
```

### **Performance İçin:**
```xml
<system.webServer>
  <httpCompression>
    <dynamicTypes>
      <add mimeType="application/json" enabled="true" />
    </dynamicTypes>
  </httpCompression>
</system.webServer>
```

## 8. **SONUÇ**

### ✅ **Çözüm Durumu:**
- **Full Trust**: ✅ Web.config'te ayarlandı
- **Medium Trust Uyumluluk**: ✅ SafeUrlHelper ile sağlandı
- **Tüm URL'ler**: ✅ Güvenli hale getirildi
- **Compile**: ✅ Başarılı (.NET Framework 4.8)

### 🚀 **Deployment Hazır:**
Proje artık hem Full Trust hem de Medium Trust sunucularda çalışacak şekilde hazırlandı.

### 📞 **Hosting Desteği:**
Eğer hala sorun yaşıyorsanız, hosting sağlayıcınızdan:
- **Trust Level'ı Full** yapmasını isteyin
- Veya **Medium Trust'ta hangi permission'ların** eksik olduğunu sorun
