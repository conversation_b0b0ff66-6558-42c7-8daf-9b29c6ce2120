using System;
using System.Web.Mvc;

namespace YasinKaratas.Web.Controllers
{
    public class ErrorController : Controller
    {
        // GET: Error
        public ActionResult Index()
        {
            ViewBag.Title = "<PERSON><PERSON> - <PERSON><PERSON>";
            return View();
        }

        // GET: Error/NotFound
        public ActionResult NotFound()
        {
            ViewBag.Title = "<PERSON>fa Bulunamadı - Ya<PERSON>";
            Response.StatusCode = 404;
            return View();
        }

        // GET: Error/ServerError
        public ActionResult ServerError()
        {
            ViewBag.Title = "<PERSON><PERSON><PERSON> - <PERSON>";
            Response.StatusCode = 500;
            return View();
        }
    }
}
