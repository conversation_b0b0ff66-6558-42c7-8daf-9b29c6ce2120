# Türkçe Karakter Sorunu Çözümü

## 🔤 **TÜRKÇE KARAKTER AYARLARI**

### ✅ **<PERSON><PERSON><PERSON><PERSON>meler:**

1. **Web.config Globalization Ayarları:**
```xml
<globalization culture="tr-TR" uiCulture="tr-TR" 
               fileEncoding="utf-8" 
               requestEncoding="utf-8" 
               responseEncoding="utf-8" />
```

2. **HTTP Runtime Encoding:**
```xml
<httpRuntime targetFramework="4.8" 
             enableVersionHeader="false" 
             requestValidationMode="2.0" 
             encoderType="System.Web.Security.AntiXss.AntiXssEncoder, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
```

3. **Response Headers:**
```xml
<customHeaders>
  <add name="Content-Type" value="text/html; charset=utf-8" />
</customHeaders>
```

4. **Layout Meta Charset:**
```html
<meta charset="utf-8" />
```

## 🧪 **TEST SAYFASI**

### **Türkçe Karakter Testi:**
- **URL**: `http://localhost:44300/turkce-test.html`
- **Test Karakterleri**: ç ğ ı ö ş ü Ç Ğ İ Ö Ş Ü
- **Arapça Test**: بسم الله الرحمن الرحيم

### **Test Adımları:**
1. Ana sayfayı açın: `/`
2. Test sayfasını açın: `/turkce-test.html`
3. Türkçe karakterlerin düzgün göründüğünü kontrol edin

## 🚀 **SUNUCU DEPLOYMENT**

### **1. Dosya Encoding Kontrol:**
Sunucuya yüklerken dosyaların **UTF-8** encoding'de olduğundan emin olun:
- **FTP Client**: UTF-8 mode seçin
- **File Manager**: Encoding ayarını kontrol edin
- **Text Editor**: UTF-8 BOM ile kaydedin

### **2. IIS Ayarları (Eğer IIS kullanıyorsanız):**
```
1. IIS Manager > Sites > yasinkaratas.com.tr
2. ASP.NET > Globalization
3. Culture: tr-TR
4. UI Culture: tr-TR
5. File Encoding: utf-8
6. Request Encoding: utf-8
7. Response Encoding: utf-8
```

### **3. Apache Ayarları (Eğer Apache kullanıyorsanız):**
`.htaccess` dosyasına ekleyin:
```apache
AddDefaultCharset UTF-8
AddCharset UTF-8 .html .htm .css .js
```

### **4. Database Encoding:**
SQL Server için:
```sql
-- Database collation
ALTER DATABASE yasinkaratas_db 
COLLATE Turkish_CI_AS;

-- Table collation
CREATE TABLE Articles (
    Title NVARCHAR(255) COLLATE Turkish_CI_AS,
    Content NTEXT COLLATE Turkish_CI_AS
);
```

## 🔧 **SORUN GİDERME**

### **A) Türkçe Karakterler Soru İşareti (?) Görünüyor:**
```
Çözüm:
1. Web.config'te encoding ayarlarını kontrol edin
2. Dosyaları UTF-8 BOM ile yeniden kaydedin
3. Response headers'da charset=utf-8 olduğunu kontrol edin
```

### **B) Türkçe Karakterler Garip Semboller Görünüyor:**
```
Çözüm:
1. Browser'da View > Encoding > UTF-8 seçin
2. Meta charset tag'inin doğru olduğunu kontrol edin
3. Server response headers'ı kontrol edin
```

### **C) Database'den Gelen Veriler Bozuk:**
```
Çözüm:
1. Database collation'ı Turkish_CI_AS yapın
2. Connection string'e charset=utf8 ekleyin
3. NVARCHAR kullanın, VARCHAR değil
```

### **D) CSS/JS Dosyalarında Türkçe Karakterler Bozuk:**
```
Çözüm:
1. CSS/JS dosyalarını UTF-8 BOM ile kaydedin
2. @charset "UTF-8"; CSS'in başına ekleyin
3. Server'da static file encoding ayarlarını kontrol edin
```

## 📊 **TEST SONUÇLARI**

### **Local Test:**
- ✅ Ana Sayfa: Türkçe karakterler düzgün
- ✅ Test Sayfası: Tüm karakterler doğru
- ✅ CSS: Türkçe metinler düzgün
- ✅ JavaScript: Türkçe tarih formatı çalışıyor

### **Sunucu Test Checklist:**
- [ ] Ana sayfa Türkçe karakterler
- [ ] Menü linkleri (Hakkında, İletişim vb.)
- [ ] Footer metinleri
- [ ] Error sayfaları (404, 500)
- [ ] Meta description/keywords
- [ ] Database verileri (eğer varsa)

## 🎯 **SONUÇ**

### ✅ **Çözülen Sorunlar:**
- **Web.config**: UTF-8 encoding ayarları
- **Response Headers**: Charset belirtildi
- **Meta Tags**: UTF-8 charset
- **Test Sayfası**: Karakter testi eklendi

### 🚀 **Deployment Ready:**
Proje artık Türkçe karakterleri doğru gösterecek şekilde yapılandırıldı.

### 📞 **Hala Sorun Varsa:**
1. **turkce-test.html** sayfasını açın
2. Karakterlerin düzgün göründüğünü kontrol edin
3. Browser'da "View Source" yapıp encoding'i kontrol edin
4. Network tab'da response headers'ı kontrol edin

**Türkçe karakterler artık tüm sayfalarda düzgün görünecek!** 🇹🇷
