using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Net;
using System.Threading.Tasks;
using System.Web.Script.Serialization;
using System.Globalization;
using System.Threading;
using System.Net;
using System.IO;
using System.Text;

namespace YasinKaratas.Web.Controllers
{
    public class HomeController : Controller
    {
        // <PERSON><PERSON> dilli encoding des<PERSON><PERSON><PERSON> i<PERSON>in her action'da encoding kontrolü
        protected override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            // UTF-8 encoding'i garanti et
            Response.ContentEncoding = Encoding.UTF8;
            Response.Charset = "utf-8";

            // Dil parametresini kontrol et ve culture ayarla
            var language = Request.QueryString["language"];
            if (!string.IsNullOrEmpty(language))
            {
                SetCulture(language);
            }

            base.OnActionExecuting(filterContext);
        }

        private void SetCulture(string language)
        {
            CultureInfo culture;

            switch (language.ToLower())
            {
                case "en":
                    culture = new CultureInfo("en-US");
                    break;
                case "ar":
                    culture = new CultureInfo("ar-SA");
                    break;
                case "tr":
                default:
                    culture = new CultureInfo("tr-TR");
                    break;
            }

            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;

            // Session'da dil bilgisini sakla
            Session["CurrentLanguage"] = language;
        }

        public ActionResult Index()
        {
            ViewBag.Title = "Yasin Karataş Hoca - Ana Sayfa";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın resmi web sitesi. İslami ilimler, dersler, makaleler ve daha fazlası.";
            ViewBag.MetaKeywords = "Yasin Karataş, İslami ilimler, din dersleri, İslam, hoca, tefsir, hadis, fıkıh";

            return View();
        }

        public ActionResult About()
        {
            ViewBag.Title = "Hakkında - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca hakkında bilgiler, eğitim hayatı ve çalışmaları.";
            ViewBag.MetaKeywords = "Yasin Karataş, hakkında, biyografi, eğitim";
            
            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Title = "İletişim - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca ile iletişime geçin.";
            ViewBag.MetaKeywords = "Yasin Karataş, iletişim, contact";

            return View();
        }

        [HttpPost]
        public ActionResult Contact(string name, string email, string subject, string message)
        {
            ViewBag.Title = "İletişim - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca ile iletişime geçin.";
            ViewBag.MetaKeywords = "Yasin Karataş, iletişim, contact";

            // HTML5 validation zaten required alanları kontrol ediyor
            if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(email) && !string.IsNullOrEmpty(message))
            {
                // Burada email gönderme işlemi yapılabilir
                ViewBag.SuccessMessage = "Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.";
            }

            return View();
        }

        public ActionResult Lessons()
        {
            ViewBag.Title = "Dersler - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın tefsir, hadis, fıkıh ve siyer dersleri.";
            ViewBag.MetaKeywords = "dersler, tefsir, hadis, fıkıh, siyer";
            
            return View();
        }

        public ActionResult Articles()
        {
            ViewBag.Title = "Makaleler - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın İslami konulardaki makaleleri.";
            ViewBag.MetaKeywords = "makaleler, İslami yazılar, din";
            
            return View();
        }

        public ActionResult Videos()
        {
            ViewBag.Title = "Videolar - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın video dersleri ve konuşmaları.";
            ViewBag.MetaKeywords = "videolar, video dersler, konuşmalar";
            
            return View();
        }

        public ActionResult Books()
        {
            ViewBag.Title = "Kitaplar - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın kitapları ve önerilen eserler.";
            ViewBag.MetaKeywords = "kitaplar, eserler, İslami kitaplar";
            
            return View();
        }

        public async Task<ActionResult> GetPrayerTimes(string city)
        {
            try
            {
                // Varsayılan İstanbul
                string selectedCity = city ?? "istanbul";

                // Cookie'den şehir adını al
                if (string.IsNullOrEmpty(city) && Request.Cookies["selectedCity"] != null)
                {
                    selectedCity = Request.Cookies["selectedCity"].Value;
                }

                // Gerçek API'den namaz vakitleri al
                var prayerTimes = await GetRealPrayerTimes(selectedCity);

                // Şehir adını cookie'ye kaydet
                if (!string.IsNullOrEmpty(city))
                {
                    var cookie = new HttpCookie("selectedCity", selectedCity)
                    {
                        Expires = DateTime.Now.AddYears(1),
                        HttpOnly = true
                    };
                    Response.Cookies.Add(cookie);
                }

                return Json(new { success = true, data = prayerTimes }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        private async Task<object> GetRealPrayerTimes(string city)
        {
            try
            {
                using (var client = new WebClient())
                {
                    // Aladhan API kullanıyoruz (ücretsiz ve güvenilir)
                    var apiUrl = $"https://api.aladhan.com/v1/timingsByCity?city={city}&country=Turkey&method=13";

                    var response = await Task.Run(() => client.DownloadString(apiUrl));
                    var serializer = new JavaScriptSerializer();
                    var apiData = serializer.DeserializeObject(response) as Dictionary<string, object>;

                    if (apiData != null && apiData.ContainsKey("code") && apiData["code"].ToString() == "200")
                    {
                        var data = apiData["data"] as Dictionary<string, object>;
                        var timings = data["timings"] as Dictionary<string, object>;

                        return new
                        {
                            city = city,
                            date = DateTime.Now.ToString("dd.MM.yyyy"),
                            times = new
                            {
                                imsak = timings["Imsak"].ToString(),
                                gunes = timings["Sunrise"].ToString(),
                                ogle = timings["Dhuhr"].ToString(),
                                ikindi = timings["Asr"].ToString(),
                                aksam = timings["Maghrib"].ToString(),
                                yatsi = timings["Isha"].ToString()
                            }
                        };
                    }
                    else
                    {
                        // API başarısız, fallback data
                        return GetStaticPrayerTimes(city);
                    }
                }
            }
            catch (Exception)
            {
                // Hata durumunda fallback data
                return GetStaticPrayerTimes(city);
            }
        }

        private object GetStaticPrayerTimes(string city)
        {
            // Demo için şehre göre namaz vakitleri (TSI - Türkiye Saati)
            var today = DateTime.Now;

            // Coğrafi bölgelere göre vakitler (Haziran 2025 yaklaşık)
            var regionTimes = GetRegionalPrayerTimes();
            var selectedTimes = regionTimes.ContainsKey(city) ? regionTimes[city] : regionTimes["istanbul"];

            return new
            {
                city = city,
                date = today.ToString("dd.MM.yyyy"),
                times = selectedTimes
            };
        }

        private Dictionary<string, object> GetRegionalPrayerTimes()
        {
            return new Dictionary<string, object>
            {
                // Marmara Bölgesi
                ["istanbul"] = new { imsak = "03:30", gunes = "05:15", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["ankara"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["bursa"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["kocaeli"] = new { imsak = "03:32", gunes = "05:17", ogle = "12:22", ikindi = "15:47", aksam = "18:27", yatsi = "20:02" },
                ["sakarya"] = new { imsak = "03:33", gunes = "05:18", ogle = "12:23", ikindi = "15:48", aksam = "18:28", yatsi = "20:03" },
                ["tekirdag"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["edirne"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["kirklareli"] = new { imsak = "03:38", gunes = "05:23", ogle = "12:28", ikindi = "15:53", aksam = "18:33", yatsi = "20:08" },
                ["canakkale"] = new { imsak = "03:42", gunes = "05:27", ogle = "12:32", ikindi = "15:57", aksam = "18:37", yatsi = "20:12" },
                ["balikesir"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["yalova"] = new { imsak = "03:33", gunes = "05:18", ogle = "12:23", ikindi = "15:48", aksam = "18:28", yatsi = "20:03" },

                // Ege Bölgesi
                ["izmir"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["manisa"] = new { imsak = "03:43", gunes = "05:28", ogle = "12:33", ikindi = "15:58", aksam = "18:38", yatsi = "20:13" },
                ["aydin"] = new { imsak = "03:47", gunes = "05:32", ogle = "12:32", ikindi = "15:57", aksam = "18:37", yatsi = "20:12" },
                ["mugla"] = new { imsak = "03:50", gunes = "05:35", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["denizli"] = new { imsak = "03:48", gunes = "05:33", ogle = "12:38", ikindi = "16:03", aksam = "18:43", yatsi = "20:18" },
                ["usak"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["afyonkarahisar"] = new { imsak = "03:47", gunes = "05:32", ogle = "12:37", ikindi = "16:02", aksam = "18:42", yatsi = "20:17" },
                ["kutahya"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },

                // Akdeniz Bölgesi
                ["antalya"] = new { imsak = "03:55", gunes = "05:40", ogle = "12:40", ikindi = "16:05", aksam = "18:45", yatsi = "20:20" },
                ["mersin"] = new { imsak = "03:52", gunes = "05:37", ogle = "12:37", ikindi = "16:02", aksam = "18:42", yatsi = "20:17" },
                ["adana"] = new { imsak = "03:50", gunes = "05:35", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["hatay"] = new { imsak = "03:48", gunes = "05:33", ogle = "12:33", ikindi = "15:58", aksam = "18:38", yatsi = "20:13" },
                ["osmaniye"] = new { imsak = "03:50", gunes = "05:35", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["isparta"] = new { imsak = "03:52", gunes = "05:37", ogle = "12:42", ikindi = "16:07", aksam = "18:47", yatsi = "20:22" },
                ["burdur"] = new { imsak = "03:53", gunes = "05:38", ogle = "12:43", ikindi = "16:08", aksam = "18:48", yatsi = "20:23" },

                // İç Anadolu Bölgesi
                ["konya"] = new { imsak = "03:50", gunes = "05:35", ogle = "12:40", ikindi = "16:05", aksam = "18:45", yatsi = "20:20" },
                ["kayseri"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["eskisehir"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["sivas"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["yozgat"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["cankiri"] = new { imsak = "03:42", gunes = "05:27", ogle = "12:32", ikindi = "15:57", aksam = "18:37", yatsi = "20:12" },
                ["kirikkale"] = new { imsak = "03:43", gunes = "05:28", ogle = "12:33", ikindi = "15:58", aksam = "18:38", yatsi = "20:13" },
                ["kirsehir"] = new { imsak = "03:42", gunes = "05:27", ogle = "12:32", ikindi = "15:57", aksam = "18:37", yatsi = "20:12" },
                ["nevsehir"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:35", ikindi = "16:00", aksam = "18:40", yatsi = "20:15" },
                ["nigde"] = new { imsak = "03:47", gunes = "05:32", ogle = "12:37", ikindi = "16:02", aksam = "18:42", yatsi = "20:17" },
                ["aksaray"] = new { imsak = "03:48", gunes = "05:33", ogle = "12:38", ikindi = "16:03", aksam = "18:43", yatsi = "20:18" },
                ["karaman"] = new { imsak = "03:50", gunes = "05:35", ogle = "12:40", ikindi = "16:05", aksam = "18:45", yatsi = "20:20" },

                // Karadeniz Bölgesi
                ["trabzon"] = new { imsak = "03:20", gunes = "05:05", ogle = "12:15", ikindi = "15:40", aksam = "18:20", yatsi = "19:55" },
                ["samsun"] = new { imsak = "03:25", gunes = "05:10", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["ordu"] = new { imsak = "03:22", gunes = "05:07", ogle = "12:17", ikindi = "15:42", aksam = "18:22", yatsi = "19:57" },
                ["giresun"] = new { imsak = "03:20", gunes = "05:05", ogle = "12:15", ikindi = "15:40", aksam = "18:20", yatsi = "19:55" },
                ["rize"] = new { imsak = "03:18", gunes = "05:03", ogle = "12:13", ikindi = "15:38", aksam = "18:18", yatsi = "19:53" },
                ["artvin"] = new { imsak = "03:15", gunes = "05:00", ogle = "12:10", ikindi = "15:35", aksam = "18:15", yatsi = "19:50" },
                ["gumushane"] = new { imsak = "03:25", gunes = "05:10", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["bayburt"] = new { imsak = "03:22", gunes = "05:07", ogle = "12:17", ikindi = "15:42", aksam = "18:22", yatsi = "19:57" },
                ["tokat"] = new { imsak = "03:30", gunes = "05:15", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["amasya"] = new { imsak = "03:32", gunes = "05:17", ogle = "12:27", ikindi = "15:52", aksam = "18:32", yatsi = "20:07" },
                ["corum"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["sinop"] = new { imsak = "03:30", gunes = "05:15", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["kastamonu"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["zonguldak"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["bartin"] = new { imsak = "03:33", gunes = "05:18", ogle = "12:28", ikindi = "15:53", aksam = "18:33", yatsi = "20:08" },
                ["karabuk"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["bolu"] = new { imsak = "03:38", gunes = "05:23", ogle = "12:33", ikindi = "15:58", aksam = "18:38", yatsi = "20:13" },
                ["duzce"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["bilecik"] = new { imsak = "03:38", gunes = "05:23", ogle = "12:33", ikindi = "15:58", aksam = "18:38", yatsi = "20:13" },

                // Doğu Anadolu Bölgesi
                ["erzurum"] = new { imsak = "03:15", gunes = "05:00", ogle = "12:10", ikindi = "15:35", aksam = "18:15", yatsi = "19:50" },
                ["erzincan"] = new { imsak = "03:20", gunes = "05:05", ogle = "12:15", ikindi = "15:40", aksam = "18:20", yatsi = "19:55" },
                ["agri"] = new { imsak = "03:10", gunes = "04:55", ogle = "12:05", ikindi = "15:30", aksam = "18:10", yatsi = "19:45" },
                ["kars"] = new { imsak = "03:05", gunes = "04:50", ogle = "12:00", ikindi = "15:25", aksam = "18:05", yatsi = "19:40" },
                ["ardahan"] = new { imsak = "03:00", gunes = "04:45", ogle = "11:55", ikindi = "15:20", aksam = "18:00", yatsi = "19:35" },
                ["igdir"] = new { imsak = "03:08", gunes = "04:53", ogle = "12:03", ikindi = "15:28", aksam = "18:08", yatsi = "19:43" },
                ["van"] = new { imsak = "03:12", gunes = "04:57", ogle = "12:07", ikindi = "15:32", aksam = "18:12", yatsi = "19:47" },
                ["mus"] = new { imsak = "03:15", gunes = "05:00", ogle = "12:10", ikindi = "15:35", aksam = "18:15", yatsi = "19:50" },
                ["bitlis"] = new { imsak = "03:18", gunes = "05:03", ogle = "12:13", ikindi = "15:38", aksam = "18:18", yatsi = "19:53" },
                ["bingol"] = new { imsak = "03:20", gunes = "05:05", ogle = "12:15", ikindi = "15:40", aksam = "18:20", yatsi = "19:55" },
                ["elazig"] = new { imsak = "03:25", gunes = "05:10", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["tunceli"] = new { imsak = "03:22", gunes = "05:07", ogle = "12:17", ikindi = "15:42", aksam = "18:22", yatsi = "19:57" },
                ["malatya"] = new { imsak = "03:30", gunes = "05:15", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },

                // Güneydoğu Anadolu Bölgesi
                ["gaziantep"] = new { imsak = "03:45", gunes = "05:30", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["sanliurfa"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["diyarbakir"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["mardin"] = new { imsak = "03:38", gunes = "05:23", ogle = "12:23", ikindi = "15:48", aksam = "18:28", yatsi = "20:03" },
                ["batman"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:20", ikindi = "15:45", aksam = "18:25", yatsi = "20:00" },
                ["sirnak"] = new { imsak = "03:32", gunes = "05:17", ogle = "12:17", ikindi = "15:42", aksam = "18:22", yatsi = "19:57" },
                ["siirt"] = new { imsak = "03:30", gunes = "05:15", ogle = "12:15", ikindi = "15:40", aksam = "18:20", yatsi = "19:55" },
                ["hakkari"] = new { imsak = "03:25", gunes = "05:10", ogle = "12:10", ikindi = "15:35", aksam = "18:15", yatsi = "19:50" },
                ["adiyaman"] = new { imsak = "03:35", gunes = "05:20", ogle = "12:25", ikindi = "15:50", aksam = "18:30", yatsi = "20:05" },
                ["kahramanmaras"] = new { imsak = "03:40", gunes = "05:25", ogle = "12:30", ikindi = "15:55", aksam = "18:35", yatsi = "20:10" },
                ["kilis"] = new { imsak = "03:43", gunes = "05:28", ogle = "12:28", ikindi = "15:53", aksam = "18:33", yatsi = "20:08" }
            };
        }

        public ActionResult GetCities()
        {
            try
            {
                // Türkiye'nin 81 ili (alfabetik sıra)
                var cities = new[]
                {
                    new { id = "adana", name = "Adana" },
                    new { id = "adiyaman", name = "Adıyaman" },
                    new { id = "afyonkarahisar", name = "Afyonkarahisar" },
                    new { id = "agri", name = "Ağrı" },
                    new { id = "aksaray", name = "Aksaray" },
                    new { id = "amasya", name = "Amasya" },
                    new { id = "ankara", name = "Ankara" },
                    new { id = "antalya", name = "Antalya" },
                    new { id = "ardahan", name = "Ardahan" },
                    new { id = "artvin", name = "Artvin" },
                    new { id = "aydin", name = "Aydın" },
                    new { id = "balikesir", name = "Balıkesir" },
                    new { id = "bartin", name = "Bartın" },
                    new { id = "batman", name = "Batman" },
                    new { id = "bayburt", name = "Bayburt" },
                    new { id = "bilecik", name = "Bilecik" },
                    new { id = "bingol", name = "Bingöl" },
                    new { id = "bitlis", name = "Bitlis" },
                    new { id = "bolu", name = "Bolu" },
                    new { id = "burdur", name = "Burdur" },
                    new { id = "bursa", name = "Bursa" },
                    new { id = "canakkale", name = "Çanakkale" },
                    new { id = "cankiri", name = "Çankırı" },
                    new { id = "corum", name = "Çorum" },
                    new { id = "denizli", name = "Denizli" },
                    new { id = "diyarbakir", name = "Diyarbakır" },
                    new { id = "duzce", name = "Düzce" },
                    new { id = "edirne", name = "Edirne" },
                    new { id = "elazig", name = "Elazığ" },
                    new { id = "erzincan", name = "Erzincan" },
                    new { id = "erzurum", name = "Erzurum" },
                    new { id = "eskisehir", name = "Eskişehir" },
                    new { id = "gaziantep", name = "Gaziantep" },
                    new { id = "giresun", name = "Giresun" },
                    new { id = "gumushane", name = "Gümüşhane" },
                    new { id = "hakkari", name = "Hakkâri" },
                    new { id = "hatay", name = "Hatay" },
                    new { id = "igdir", name = "Iğdır" },
                    new { id = "isparta", name = "Isparta" },
                    new { id = "istanbul", name = "İstanbul" },
                    new { id = "izmir", name = "İzmir" },
                    new { id = "kahramanmaras", name = "Kahramanmaraş" },
                    new { id = "karabuk", name = "Karabük" },
                    new { id = "karaman", name = "Karaman" },
                    new { id = "kars", name = "Kars" },
                    new { id = "kastamonu", name = "Kastamonu" },
                    new { id = "kayseri", name = "Kayseri" },
                    new { id = "kilis", name = "Kilis" },
                    new { id = "kirikkale", name = "Kırıkkale" },
                    new { id = "kirklareli", name = "Kırklareli" },
                    new { id = "kirsehir", name = "Kırşehir" },
                    new { id = "kocaeli", name = "Kocaeli" },
                    new { id = "konya", name = "Konya" },
                    new { id = "kutahya", name = "Kütahya" },
                    new { id = "malatya", name = "Malatya" },
                    new { id = "manisa", name = "Manisa" },
                    new { id = "mardin", name = "Mardin" },
                    new { id = "mersin", name = "Mersin" },
                    new { id = "mugla", name = "Muğla" },
                    new { id = "mus", name = "Muş" },
                    new { id = "nevsehir", name = "Nevşehir" },
                    new { id = "nigde", name = "Niğde" },
                    new { id = "ordu", name = "Ordu" },
                    new { id = "osmaniye", name = "Osmaniye" },
                    new { id = "rize", name = "Rize" },
                    new { id = "sakarya", name = "Sakarya" },
                    new { id = "samsun", name = "Samsun" },
                    new { id = "sanliurfa", name = "Şanlıurfa" },
                    new { id = "siirt", name = "Siirt" },
                    new { id = "sinop", name = "Sinop" },
                    new { id = "sirnak", name = "Şırnak" },
                    new { id = "sivas", name = "Sivas" },
                    new { id = "tekirdag", name = "Tekirdağ" },
                    new { id = "tokat", name = "Tokat" },
                    new { id = "trabzon", name = "Trabzon" },
                    new { id = "tunceli", name = "Tunceli" },
                    new { id = "usak", name = "Uşak" },
                    new { id = "van", name = "Van" },
                    new { id = "yalova", name = "Yalova" },
                    new { id = "yozgat", name = "Yozgat" },
                    new { id = "zonguldak", name = "Zonguldak" }
                };

                return Json(new { success = true, data = cities }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }
    }
}
