using System;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Globalization;
using System.Threading;
using System.Text;

namespace YasinKaratas.Web
{
    public class MvcApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
        }

        protected void Application_BeginRequest()
        {
            // UTF-8 encoding ayarla - Tüm diller için kritik
            Response.ContentEncoding = Encoding.UTF8;
            Response.Charset = "utf-8";
            Request.ContentEncoding = Encoding.UTF8;

            // HTTP Header'da charset belirt
            Response.Headers.Add("Content-Type", "text/html; charset=utf-8");

            // Varsayılan Türkçe culture ayarla
            var culture = new CultureInfo("tr-TR");
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }

        protected void Application_PreSendRequestHeaders()
        {
            // Response header'larında charset'i garanti et
            Response.Headers.Remove("Server");
            if (!Response.ContentType.Contains("charset"))
            {
                Response.ContentType += "; charset=utf-8";
            }
        }
    }
}
