# Production Deployment Rehberi

## 🚀 **SUNUCU DEPLOYMENT ADIMLARI**

### ❌ **Çözü<PERSON> Sorun:**
```
Configuration Error: This configuration section cannot be used at this path.
<trust level="Full" /> - Hosting sağlayıcısı tarafından kilitlenmiş
```

### ✅ **Çözüm:**
- Trust level Web.config'ten kaldırıldı
- SafeUrlHelper Medium Trust uyumlu
- Production ayarları yapıldı

## 1. **DOSYA KOPYALAMA**

### **A) Tüm Proje Dosyalarını Kopyalayın:**
```
yasinkaratas.com.tr/httpdocs/
├── bin/                    ← TÜM DLL'LER (ÖNEMLİ!)
├── Views/                  ← Razor view'lar
├── Content/                ← CSS dosyaları
├── Scripts/                ← JavaScript dosyaları (boş olabilir)
├── Helpers/                ← SafeUrlHelper.cs
├── Controllers/            ← HomeController.cs
├── App_Start/              ← Config dosyaları
├── Global.asax             ← MVC başlatma
├── Web.config              ← Ana konfigürasyon
└── favicon.ico             ← Site ikonu
```

### **B) Kritik Dosyalar:**
```
✅ bin/YasinKaratas.Web.dll
✅ bin/System.Web.Mvc.dll
✅ bin/System.Web.Optimization.dll
✅ bin/System.Web.WebPages.dll
✅ bin/EntityFramework.dll
✅ Web.config (trust level kaldırılmış)
```

## 2. **WEB.CONFIG AYARLARI**

### **Production Web.config:**
```xml
<system.web>
  <!-- DEBUG KAPALI -->
  <compilation debug="false" targetFramework="4.8" />
  
  <!-- CUSTOM ERRORS AÇIK -->
  <customErrors mode="On" defaultRedirect="~/Error">
    <error statusCode="404" redirect="~/Error/NotFound" />
    <error statusCode="500" redirect="~/Error/ServerError" />
  </customErrors>
  
  <!-- TRUST LEVEL YOK (Sunucu yönetir) -->
</system.web>
```

### **Connection String Güncelleyin:**
```xml
<connectionStrings>
  <add name="DefaultConnection" 
       connectionString="Server=localhost;Database=yasinkaratas_db;User Id=yasinkaratas_user;Password=YOUR_PASSWORD;MultipleActiveResultSets=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

## 3. **HOSTING PANEL AYARLARI**

### **A) .NET Framework Sürümü:**
- **.NET Framework 4.8** seçin
- **NOT**: .NET Core değil!

### **B) Application Pool:**
- **Integrated Pipeline Mode**
- **No Managed Code** değil, **.NET Framework v4.0** seçin

### **C) Default Document:**
- **Default.aspx** kaldırın
- Sadece **index.html** bırakın (MVC routing kullanıyor)

## 4. **DATABASE AYARLARI**

### **A) SQL Server Database:**
1. Hosting panelinden database oluşturun
2. Connection string'i güncelleyin
3. Database user'ı oluşturun

### **B) Entity Framework:**
- Code First kullanıyorsak migration çalıştırın
- Database First kullanıyorsak .edmx dosyasını kopyalayın

## 5. **TEST ADIMLARI**

### **1. Temel Test:**
```
✅ https://yasinkaratas.com.tr → Ana sayfa açılıyor mu?
✅ https://yasinkaratas.com.tr/Home/About → Hakkında sayfası
✅ Menü linkleri çalışıyor mu?
✅ CSS yükleniyor mu?
```

### **2. Hata Kontrolü:**
```
❌ 500 Internal Server Error → Web.config hatası
❌ 404 Not Found → Routing sorunu
❌ Compilation Error → DLL eksik
❌ Security Exception → Trust level sorunu (SafeUrlHelper devreye girer)
```

## 6. **YAYGIN SORUNLAR VE ÇÖZÜMLERİ**

### **A) "Could not load file or assembly"**
```
Çözüm: bin/ klasöründeki tüm DLL'leri kontrol edin
- System.Web.Mvc.dll
- System.Web.Optimization.dll
- System.Web.WebPages.dll
- EntityFramework.dll
```

### **B) "The view 'Index' or its master was not found"**
```
Çözüm: Views/ klasörünü tamamen kopyalayın
- Views/Home/Index.cshtml
- Views/Shared/_Layout.cshtml
- Views/Web.config
```

### **C) "HTTP Error 500.19 - Configuration Error"**
```
Çözüm: Web.config syntax hatası
- XML geçerli mi?
- Kilitli section var mı? (trust level gibi)
```

### **D) "Security Exception"**
```
Çözüm: SafeUrlHelper otomatik devreye girer
- Medium Trust uyumlu kod çalışır
- Manuel URL'ler oluşturulur
```

## 7. **PERFORMANCE OPTİMİZASYONU**

### **A) Web.config Optimizasyonu:**
```xml
<system.web>
  <compilation debug="false" />
  <httpRuntime enableVersionHeader="false" />
</system.web>

<system.webServer>
  <httpCompression>
    <dynamicTypes>
      <add mimeType="application/json" enabled="true" />
    </dynamicTypes>
  </httpCompression>
</system.webServer>
```

### **B) Static Content Caching:**
```xml
<staticContent>
  <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="7.00:00:00" />
</staticContent>
```

## 8. **GÜVENLİK AYARLARI**

### **A) Security Headers:**
```xml
<httpProtocol>
  <customHeaders>
    <add name="X-Content-Type-Options" value="nosniff" />
    <add name="X-Frame-Options" value="SAMEORIGIN" />
    <add name="X-XSS-Protection" value="1; mode=block" />
  </customHeaders>
</httpProtocol>
```

### **B) HTTPS Redirect (Opsiyonel):**
```xml
<!-- Sadece HTTPS sertifikası varsa -->
<rewrite>
  <rules>
    <rule name="Redirect to HTTPS" stopProcessing="true">
      <match url=".*" />
      <conditions>
        <add input="{HTTPS}" pattern="off" ignoreCase="true" />
      </conditions>
      <action type="Redirect" url="https://{HTTP_HOST}/{R:0}" redirectType="Permanent" />
    </rule>
  </rules>
</rewrite>
```

## 9. **DEPLOYMENT CHECKLIST**

### **Deployment Öncesi:**
- ✅ Local'de test edildi
- ✅ Debug mode kapatıldı
- ✅ Connection string güncellendi
- ✅ Trust level kaldırıldı
- ✅ Tüm DLL'ler bin klasöründe

### **Deployment Sonrası:**
- ✅ Ana sayfa açılıyor
- ✅ Menü navigasyonu çalışıyor
- ✅ CSS/JS yükleniyor
- ✅ Hata sayfaları test edildi
- ✅ Mobile responsive test edildi

## 10. **BACKUP VE MONITORING**

### **A) Backup:**
- Database backup'ı alın
- Dosya backup'ı alın
- Web.config backup'ı alın

### **B) Monitoring:**
- IIS logs kontrol edin
- Error logs takip edin
- Performance monitor edin

## 🎯 **SONUÇ**

Proje artık **Medium Trust** hosting ortamlarında çalışacak şekilde hazırlandı. Trust level kısıtlaması olmayan sunucularda da sorunsuz çalışacak.

**SafeUrlHelper** sayesinde güvenlik kısıtlamaları otomatik olarak handle ediliyor.
