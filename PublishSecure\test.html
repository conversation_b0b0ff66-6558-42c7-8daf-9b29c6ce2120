<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
</head>
<body>
    <h1>Namaz Vakitleri API Test</h1>
    <div id="result"></div>
    
    <script>
    $(document).ready(function() {
        console.log('Test başlıyor...');
        
        // GetPrayerTimes test
        $.ajax({
            url: '/Home/GetPrayerTimes',
            type: 'GET',
            success: function(response) {
                console.log('GetPrayerTimes yanıtı:', response);
                $('#result').html('<h3>GetPrayerTimes Sonucu:</h3><pre>' + JSON.stringify(response, null, 2) + '</pre>');
            },
            error: function(xhr, status, error) {
                console.error('GetPrayerTimes hatası:', status, error);
                $('#result').html('<h3>Hata:</h3><p>' + error + '</p>');
            }
        });
    });
    </script>
</body>
</html>
