# HTTPS Sorun Giderme Rehberi

## 🔒 **HTTPS HATALARI VE ÇÖZÜMLERİ**

### 1. **SSL Sertifika Hatası**
```
SSL connection error / Certificate not trusted
```

**<PERSON><PERSON><PERSON><PERSON><PERSON>:**
- ✅ **SSL Sertifikası Yükleyin**: Let's Encrypt, Cloudflare, veya ticari SSL
- ✅ **IIS'te SSL Binding Yapın**: Site > Bindings > Add HTTPS (443)
- ✅ **Intermediate Certificate**: Tam sertifika zincirini yükleyin

### 2. **Mixed Content Hatası**
```
Mixed Content: The page was loaded over HTTPS, but requested an insecure resource
```

**Çözüm - Layout.cshtml'de:**
```html
<!-- HTTP yerine HTTPS kullanın -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
```

### 3. **URL Rewrite Hatası**
```
HTTP Error 500.19 - Cannot read configuration file
```

**Çözüm:**
1. **IIS URL Rewrite Module** yükleyin
2. Veya Web.config'ten rewrite kurallarını kaldırın:

```xml
<!-- Bu bölümü kaldırın eğer URL Rewrite yüklü değilse -->
<rewrite>
  <rules>
    <!-- HTTPS redirect kuralları -->
  </rules>
</rewrite>
```

### 4. **HSTS Hatası**
```
Strict-Transport-Security header issues
```

**Çözüm:**
```xml
<httpProtocol>
  <customHeaders>
    <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
  </customHeaders>
</httpProtocol>
```

### 5. **IIS HTTPS Konfigürasyonu**

#### **A) SSL Sertifikası Yükleme:**
1. IIS Manager açın
2. Server Certificates > Import
3. .pfx dosyasını seçin
4. Password girin

#### **B) Site Binding:**
1. Sites > yasinkaratas.com.tr
2. Bindings > Add
3. Type: https, Port: 443
4. SSL Certificate seçin

#### **C) HTTPS Redirect (IIS'te):**
1. URL Rewrite > Add Rule
2. Blank Rule seçin
3. Pattern: `.*`
4. Conditions: `{HTTPS}` equals `off`
5. Action: Redirect to `https://{HTTP_HOST}/{R:0}`

### 6. **Web.config HTTPS Ayarları**

#### **Basit HTTPS (URL Rewrite olmadan):**
```xml
<system.web>
  <httpCookies requireSSL="true" />
  <sessionState cookieRequireSSL="true" />
</system.web>

<system.webServer>
  <httpProtocol>
    <customHeaders>
      <add name="Strict-Transport-Security" value="max-age=31536000" />
    </customHeaders>
  </httpProtocol>
</system.webServer>
```

#### **Gelişmiş HTTPS (URL Rewrite ile):**
Web.HTTPS.config dosyasını kullanın.

### 7. **Cloudflare ile HTTPS**

#### **Cloudflare Ayarları:**
- **SSL/TLS**: Full (strict)
- **Always Use HTTPS**: On
- **HSTS**: Enable
- **Minimum TLS Version**: 1.2

#### **Origin Certificate:**
1. Cloudflare'de Origin Certificate oluşturun
2. IIS'te yükleyin
3. Binding'i yapın

### 8. **Test Komutları**

#### **SSL Test:**
```bash
# SSL Labs test
https://www.ssllabs.com/ssltest/analyze.html?d=yasinkaratas.com.tr

# OpenSSL test
openssl s_client -connect yasinkaratas.com.tr:443
```

#### **HTTPS Headers Test:**
```bash
curl -I https://yasinkaratas.com.tr
```

### 9. **Yaygın Hatalar ve Çözümleri**

| Hata | Çözüm |
|------|-------|
| `NET::ERR_CERT_AUTHORITY_INVALID` | SSL sertifikası güvenilir CA'dan alın |
| `NET::ERR_CERT_COMMON_NAME_INVALID` | Sertifika domain'e uygun olmalı |
| `Mixed Content` | Tüm kaynakları HTTPS yapın |
| `HSTS Error` | HSTS header'ı doğru ayarlayın |
| `Redirect Loop` | HTTPS redirect kurallarını kontrol edin |

### 10. **Production Checklist**

- ✅ SSL Sertifikası yüklü ve geçerli
- ✅ IIS'te HTTPS binding yapılmış
- ✅ HTTP'den HTTPS'e redirect çalışıyor
- ✅ Mixed content yok
- ✅ Security headers aktif
- ✅ HSTS header ayarlanmış
- ✅ SSL Labs'te A+ rating

### 11. **Acil Durum Çözümü**

Eğer HTTPS çalışmıyorsa, geçici olarak:

1. **Web.config'te HTTPS ayarlarını kaldırın**
2. **HTTP'de çalıştırın**
3. **SSL sorununu çözün**
4. **HTTPS'e geçin**

```xml
<!-- Geçici olarak kaldırın -->
<!-- <add name="Strict-Transport-Security" value="..." /> -->
<!-- <rewrite>...</rewrite> -->
```
