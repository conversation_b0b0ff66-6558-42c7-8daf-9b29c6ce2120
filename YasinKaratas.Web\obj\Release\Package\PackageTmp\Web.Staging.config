<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  
  <!-- Staging modunda connection string'<PERSON><PERSON> -->
  <connectionStrings>
    <!-- Staging SQL Server Connection String -->
    <add name="DefaultConnection" 
         connectionString="Server=your-staging-server;Database=YasinKaratasWeb_Staging;User Id=your-staging-username;Password=your-staging-password;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False" 
         providerName="System.Data.SqlClient"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
    
    <add name="YasinKaratasContext" 
         connectionString="Server=your-staging-server;Database=YasinKaratasWeb_Staging;User Id=your-staging-username;Password=your-staging-password;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False" 
         providerName="System.Data.SqlClient"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)" />
  </connectionStrings>

  <!-- Staging modunda compilation debug=false ama trace enabled -->
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    
    <!-- Staging modunda custom errors RemoteOnly -->
    <customErrors mode="RemoteOnly" defaultRedirect="~/Error" xdt:Transform="Replace">
      <error statusCode="404" redirect="~/Error/NotFound" />
      <error statusCode="500" redirect="~/Error/ServerError" />
    </customErrors>
    
    <!-- Staging modunda trace enabled (debugging için) -->
    <trace enabled="true" pageOutput="false" requestLimit="100" localOnly="false" xdt:Transform="Replace" />
    
    <!-- Staging modunda session state ayarları -->
    <sessionState cookieless="false" regenerateExpiredSessionId="false" 
                  mode="InProc" timeout="30" xdt:Transform="Replace" />
  </system.web>

  <!-- Staging modunda IIS ayarları -->
  <system.webServer>
    <!-- Staging modunda detailed errors (debugging için) -->
    <httpErrors errorMode="DetailedLocalOnly" xdt:Transform="Replace" />
    
    <!-- Staging modunda basic compression -->
    <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
      <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
      </staticTypes>
    </httpCompression>
    
    <!-- Staging modunda basic security headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="SAMEORIGIN" />
        <add name="X-XSS-Protection" value="1; mode=block" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>

  <!-- Staging modunda app settings -->
  <appSettings>
    <add key="Environment" value="Staging" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="EnableDetailedErrors" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
  </appSettings>

</configuration>
