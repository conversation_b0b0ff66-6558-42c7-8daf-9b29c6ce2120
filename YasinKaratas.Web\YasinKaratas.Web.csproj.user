<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LastActiveSolutionConfig>Release|Any CPU</LastActiveSolutionConfig>
    <NameOfLastUsedPublishProfile>D:\Yasin\yasinkaratas.com.tr\YasinKaratas.Web\Properties\PublishProfiles\FolderProfile.pubxml</NameOfLastUsedPublishProfile>
  </PropertyGroup>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>True</EnableENC>
          <AlwaysStartWebServerOnDebug>True</AlwaysStartWebServerOnDebug>
          <UseIISExpress>true</UseIISExpress>
          <IISExpressSSLPort>44300</IISExpressSSLPort>
          <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
          <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
          <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
          <DevelopmentServerPort>44300</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>