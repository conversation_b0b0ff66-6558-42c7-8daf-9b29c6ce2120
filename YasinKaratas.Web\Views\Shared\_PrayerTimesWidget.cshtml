<!-- Prayer Times Widget (Partial View) -->
<div class="prayer-times-widget">
    <div class="widget" style="background: white; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 2px 12px rgba(0,0,0,0.06); overflow: hidden; border: 1px solid rgba(0,0,0,0.04);">
        <div class="widget-header" style="background: #5a6c7d; color: white; padding: 15px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h6 style="margin: 0; font-weight: 500; font-family: 'Inter', sans-serif;">Namaz Vakitler<PERSON></h6>
                <div style="display: flex; align-items: center;">
                    <i class="fas fa-map-marker-alt" style="margin-right: 5px; font-size: 12px;"></i>
                    <span id="prayer-city-name" style="font-size: 12px;">İstanbul</span>
                </div>
            </div>
            <div style="margin-top: 10px;">
                <select id="city-selector" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 5px 8px; border-radius: 4px; font-size: 12px; width: 100%;">
                    <option value="">Şehir Seçin...</option>
                </select>
            </div>
        </div>
        <div class="widget-content" style="padding: 20px;">
            <div id="prayer-date" style="text-align: center; color: #7f8c8d; font-size: 12px; margin-bottom: 15px; font-weight: 500;">
                Yükleniyor...
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                <span style="color: #2c3e50;">İmsak</span>
                <span id="imsak-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                <span style="color: #2c3e50;">Güneş</span>
                <span id="gunes-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                <span style="color: #2c3e50;">Öğle</span>
                <span id="ogle-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                <span style="color: #2c3e50;">İkindi</span>
                <span id="ikindi-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                <span style="color: #2c3e50;">Akşam</span>
                <span id="aksam-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
            <div class="prayer-time" style="display: flex; justify-content: space-between; padding: 8px 0;">
                <span style="color: #2c3e50;">Yatsı</span>
                <span id="yatsi-time" style="color: #7f8c8d; font-weight: 600;">--:--</span>
            </div>
        </div>
    </div>
</div>

<style>
#city-selector option {
    background: #2c3e50;
    color: white;
}

.prayer-times-widget .prayer-time:hover {
    background-color: rgba(0,0,0,0.02);
}

@@media (max-width: 768px) {
    .prayer-times-widget .widget {
        margin-bottom: 20px;
    }
}
</style>

<script>
$(document).ready(function() {
    // Namaz vakitleri widget'ı başlat
    initPrayerTimesWidget();
});

function initPrayerTimesWidget() {
    console.log('Namaz vakitleri widget başlatılıyor...');

    // Sayfa yüklendiğinde namaz vakitlerini ve şehirleri getir
    loadPrayerTimes();
    loadCities();

    // Şehir değiştirildiğinde
    $('#city-selector').change(function() {
        var city = $(this).val();
        console.log('Şehir değişti:', city);
        if (city) {
            loadPrayerTimes(city);
            $('#prayer-city-name').text($(this).find('option:selected').text());
        }
    });
}

function loadPrayerTimes(city) {
    console.log('Namaz vakitleri yükleniyor, şehir:', city || 'varsayılan');

    $.ajax({
        url: '@Url.Action("GetPrayerTimes", "Home")',
        type: 'GET',
        data: city ? { city: city } : {},
        success: function(response) {
            console.log('API yanıtı:', response);

            try {
                if (response && response.success) {
                    var data = response.data;
                    console.log('Veri:', data);

                    // Tarih güncelle
                    $('#prayer-date').text(data.date);

                    // Vakitleri güncelle
                    $('#imsak-time').text(data.times.imsak);
                    $('#gunes-time').text(data.times.gunes);
                    $('#ogle-time').text(data.times.ogle);
                    $('#ikindi-time').text(data.times.ikindi);
                    $('#aksam-time').text(data.times.aksam);
                    $('#yatsi-time').text(data.times.yatsi);

                    // Şehir adını güncelle
                    if (data.city) {
                        var cityName = data.city.charAt(0).toUpperCase() + data.city.slice(1);
                        $('#prayer-city-name').text(cityName);
                        console.log('Şehir adı güncellendi:', cityName);
                    }

                    console.log('Namaz vakitleri başarıyla güncellendi');
                } else {
                    console.error('API başarısız:', response);
                    $('#prayer-date').text('API Hatası');
                }
            } catch (e) {
                console.error('JavaScript hatası:', e);
                $('#prayer-date').text('JavaScript Hatası: ' + e.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX hatası:', status, error);
            $('#prayer-date').text('Bağlantı hatası: ' + error);
        }
    });
}

function loadCities() {
    console.log('Şehirler yükleniyor...');

    $.ajax({
        url: '@Url.Action("GetCities", "Home")',
        type: 'GET',
        success: function(response) {
            console.log('Şehirler API yanıtı:', response);

            try {
                if (response && response.success) {
                    var cities = response.data;
                    var selector = $('#city-selector');
                    selector.empty();
                    selector.append('<option value="">Şehir Seçin...</option>');

                    $.each(cities, function(index, city) {
                        selector.append('<option value="' + city.id + '">' + city.name + '</option>');
                    });

                    console.log('Şehirler başarıyla yüklendi:', cities.length, 'şehir');
                } else {
                    console.error('Şehirler API başarısız:', response);
                }
            } catch (e) {
                console.error('Şehirler JavaScript hatası:', e);
            }
        },
        error: function(xhr, status, error) {
            console.error('Şehirler AJAX hatası:', status, error);
        }
    });
}
</script>
