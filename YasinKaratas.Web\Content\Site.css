/* ===== YASIN KARATAŞ HOCA WEB SİTESİ CSS ===== */
/* ÇOK DİLLİ DESTEK: TR, EN, AR (RTL) */

/* Gene<PERSON> - Pastel Renkler */
:root {
    --primary-color: #5a6c7d;
    --secondary-color: #7a8a9a;
    --accent-color: #a8b5c4;
    --text-color: #4a4a4a;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0,0,0,0.08);
    --pastel-blue: #8fa4b3;
    --pastel-green: #9bb5a2;
    --pastel-orange: #c4a484;
    --pastel-red: #b89090;

    /* ===== PROPORTIONAL FONT SCALING ===== */
    /* Screen width proportional scaling - maintains readability across all devices */
    --base-font-size: calc(14px + 0.4vw);
    --small-font-size: calc(12px + 0.3vw);
    --large-font-size: calc(16px + 0.5vw);
    --xl-font-size: calc(20px + 0.8vw);
    --xxl-font-size: calc(24px + 1.2vw);

    /* Navigation specific - proportional to screen width */
    --nav-font-size: calc(12px + 0.15vw);
    --nav-padding-horizontal: calc(8px + 0.2vw);
    --nav-padding-vertical: calc(10px + 0.2vw);

    /* Logo specific - scales nicely with screen */
    --logo-font-size: calc(20px + 0.8vw);
}

/* ===== ÇOK DİLLİ ENCODING VE RTL DESTEĞİ ===== */

/* UTF-8 Encoding Garantisi */
@charset "UTF-8";

/* RTL (Arapça) Dil Desteği */
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

html[dir="rtl"] .text-left {
    text-align: right !important;
}

html[dir="rtl"] .text-right {
    text-align: left !important;
}

html[dir="rtl"] .float-left {
    float: right !important;
}

html[dir="rtl"] .float-right {
    float: left !important;
}

/* RTL için margin/padding düzeltmeleri */
html[dir="rtl"] .margin-right {
    margin-right: 0;
    margin-left: 15px;
}

html[dir="rtl"] .padding-right {
    padding-right: 0;
    padding-left: 15px;
}

/* Arapça font desteği */
html[lang="ar"] {
    font-family: 'Amiri', 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
    font-size: 16px;
    line-height: 1.8;
}

/* İngilizce font optimizasyonu */
html[lang="en"] {
    font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
    font-size: 14px;
    line-height: 1.6;
}

/* Türkçe font (varsayılan) */
html[lang="tr"] {
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    font-size: 14px;
    line-height: 1.6;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    font-weight: 400;
    letter-spacing: -0.01em;
    font-size: var(--base-font-size);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* ===== HEADER STYLES ===== */

/* Top Bar */
.top-bar {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 0;
    font-size: var(--small-font-size);
}

.top-bar .contact-info span {
    margin-right: 20px;
}

.top-bar .contact-info i {
    margin-right: 5px;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 15px;
}

.language-selector .btn {
    background: transparent;
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    font-size: 12px;
    padding: 4px 12px;
}

.language-selector .btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.social-links a {
    color: white;
    margin-left: 10px;
    font-size: 14px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: var(--accent-color);
    text-decoration: none;
}

/* Main Header */
.main-header {
    background-color: white;
    padding: 8px 0;
    box-shadow: var(--shadow);
}

.site-logo .logo-text {
    color: var(--primary-color);
    font-size: var(--logo-font-size);
    font-weight: 600;
    margin: 0;
    font-family: 'Inter', 'Segoe UI', sans-serif;
    letter-spacing: -0.02em;
}

.site-logo a {
    text-decoration: none;
}

.search-box {
    max-width: 400px;
    margin: 0 auto;
    padding: 0;
}

.search-form .input-group {
    display: flex;
    align-items: stretch;
}

.search-form .input-group .form-control {
    flex: 1;
}

.search-form .input-group-btn {
    display: flex;
    align-items: stretch;
}

.search-form .input-group-btn .btn {
    border-left: none;
}

.search-form .form-control {
    border: 1px solid var(--border-color);
    border-radius: 18px 0 0 18px;
    padding: 6px 12px;
    font-size: 13px;
    height: 30px;
    line-height: 1.1;
    margin: 0;
}

.search-form .btn-search {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
    border-radius: 0 18px 18px 0;
    padding: 6px 12px;
    height: 30px;
    line-height: 1.1;
    margin: 0;
}

.search-form .btn-search:hover {
    background-color: var(--secondary-color);
}

.prayer-times-widget {
    text-align: center;
    background-color: var(--light-bg);
    padding: 15px;
    border-radius: 8px;
}

.prayer-time .prayer-name {
    display: block;
    font-size: 12px;
    color: var(--text-color);
    margin-bottom: 5px;
}

.prayer-time .prayer-clock {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}

/* ===== İSLAMWAY TARZI STILLER - PASTEL RENKLER ===== */

/* Spotlight Cards */
.spotlights-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.spotlight-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(0,0,0,0.04);
}

.spotlight-card {
    cursor: pointer;
}

.spotlight-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 24px rgba(0,0,0,0.1);
}

/* Spotlight Card Hover - Renkleri Canlandır */
.spotlight-card:hover .tefsir-gradient {
    background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
}

.spotlight-card:hover .hadis-gradient {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.spotlight-card:hover .fikih-gradient {
    background: linear-gradient(135deg, #3498db, #2980b9) !important;
}

.spotlight-card:hover .siyer-gradient {
    background: linear-gradient(135deg, #f39c12, #e67e22) !important;
}

.spotlight-image {
    height: 180px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spotlight-content {
    padding: 18px;
}

.spotlight-content h5 {
    color: var(--text-color);
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
}

.spotlight-content p {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    font-weight: 400;
}

/* Spotlight Gradient Classes */
.tefsir-gradient {
    background: linear-gradient(135deg, #9bb5a2, #b5c9ac);
}

.hadis-gradient {
    background: linear-gradient(135deg, #b89090, #c4a4a4);
}

.fikih-gradient {
    background: linear-gradient(135deg, #8fa4b3, #a4b5c4);
}

.siyer-gradient {
    background: linear-gradient(135deg, #c4a484, #d4b494);
}

/* Spotlight Icon Container */
.spotlight-icon-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
}

.spotlight-icon-container i {
    font-size: 42px;
    margin-bottom: 10px;
    opacity: 0.95;
}

.spotlight-icon-container h4 {
    margin: 0;
    font-size: 17px;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
}

/* Content Feed */
.content-feed {
    padding: 40px 0;
}

.content-card {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(0,0,0,0.04);
}

.content-card:hover {
    box-shadow: 0 6px 24px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.card-header {
    padding: 15px;
    border-bottom: 1px solid #ecf0f1;
}

.author-info {
    display: flex;
    align-items: center;
}

.author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.card-content {
    padding: 20px;
}

.audio-player {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.play-btn {
    background: var(--pastel-blue);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-right: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.play-btn:hover {
    background: #3498db;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.stats {
    display: flex;
    justify-content: space-between;
    color: #7f8c8d;
    font-size: 14px;
}

/* Widgets */
.widget {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.widget-header {
    padding: 15px;
    color: #fff;
    font-weight: 600;
}

.widget-content {
    padding: 20px;
}

.prayer-time {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
}

.scholar-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ecf0f1;
}

.scholar-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

/* Navigation */
.main-navigation {
    background-color: var(--secondary-color);
    padding: 0;
}

.main-navigation .navbar {
    margin-bottom: 0 !important;
}

.main-navigation .navbar-nav {
    margin: 0 !important;
    padding: 0 !important;
}

.main-navigation .navbar-nav > li {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    float: none !important;
}

.main-navigation .navbar {
    background-color: transparent;
    border: none;
    margin-bottom: 0;
}

.main-navigation .navbar-nav > li > a {
    color: white !important;
    padding: var(--nav-padding-vertical) 8px !important;
    font-weight: 400;
    font-size: var(--nav-font-size);
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
    letter-spacing: -0.02em;
    margin: 0 !important;
}

.main-navigation .navbar-nav > li > a:hover,
.main-navigation .navbar-nav > li > a:focus {
    background-color: var(--primary-color);
    border-bottom-color: var(--accent-color);
}

.main-navigation .navbar-nav > li > a i {
    margin-right: 6px;
    font-size: 0.9em;
}

.main-navigation .dropdown-menu {
    background-color: white;
    border: none;
    box-shadow: var(--shadow);
    border-radius: 0;
}

.main-navigation .dropdown-menu > li > a {
    padding: 12px 20px;
    color: var(--text-color) !important;
    transition: background-color 0.3s;
}

.main-navigation .dropdown-menu > li > a:hover {
    background-color: var(--light-bg);
}

/* ===== MAIN CONTENT STYLES ===== */

.main-content {
    min-height: 60vh;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--light-bg) 0%, white 100%);
    padding: 80px 0;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    font-family: 'Amiri', serif;
}

.hero-subtitle {
    font-size: 24px;
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-weight: 300;
}

.hero-description {
    font-size: 18px;
    color: var(--text-color);
    margin-bottom: 30px;
    line-height: 1.7;
}

.hero-buttons .btn {
    margin-right: 15px;
    margin-bottom: 10px;
    padding: 12px 30px;
    font-weight: 500;
    border-radius: 25px;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.hero-image {
    text-align: center;
}

.placeholder-image {
    background-color: var(--light-bg);
    border: 2px dashed var(--border-color);
    border-radius: 15px;
    padding: 60px 20px;
    color: var(--text-color);
}

.placeholder-image i {
    color: var(--primary-color);
    margin-bottom: 15px;
}

/* Section Styles */
.section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 50px;
    font-family: 'Amiri', serif;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: var(--accent-color);
    margin: 15px auto 0;
}

/* Featured Content */
.featured-content {
    padding: 80px 0;
    background-color: white;
}

.content-card {
    background-color: white;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 30px;
    height: 100%;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.content-card .card-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.content-card h4 {
    color: var(--accent-color);
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.content-card h5 {
    color: var(--primary-color);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.content-card p {
    color: var(--text-color);
    margin-bottom: 25px;
    line-height: 1.6;
}

/* Categories Section */
.categories-section {
    padding: 80px 0;
    background-color: var(--light-bg);
}

.category-card {
    background-color: white;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    transition: transform 0.3s;
    margin-bottom: 30px;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.category-icon {
    font-size: 40px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.category-card h4 {
    color: var(--primary-color);
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
}

.category-card p {
    color: var(--text-color);
    margin-bottom: 15px;
}

.content-count {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

/* Latest Content */
.latest-content {
    padding: 80px 0;
    background-color: white;
}

.latest-content h3 {
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    border-bottom: 3px solid var(--accent-color);
    padding-bottom: 10px;
}

.content-list {
    margin-bottom: 30px;
}

.content-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
}

.content-item:last-child {
    border-bottom: none;
}

.content-meta {
    margin-bottom: 10px;
}

.content-type {
    background-color: var(--primary-color);
    color: white;
    padding: 3px 10px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.content-date {
    color: #666;
    font-size: 12px;
    margin-left: 10px;
}

.content-item h5 {
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.content-item p {
    color: var(--text-color);
    margin-bottom: 0;
    font-size: 14px;
}

/* Newsletter Section */
.newsletter-section {
    padding: 60px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.newsletter-section h3 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 15px;
}

.newsletter-section p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form .form-control {
    border: none;
    border-radius: 25px 0 0 25px;
    padding: 15px 20px;
    font-size: 16px;
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    padding: 15px 30px;
    font-weight: 600;
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.newsletter-form .btn:hover {
    background-color: #b8941f;
    border-color: #b8941f;
}

/* ===== FOOTER STYLES ===== */

.site-footer {
    background-color: #2c3e50;
    color: white;
    padding: 50px 0 20px;
}

.footer-section h5 {
    color: var(--accent-color);
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-section p {
    margin-bottom: 20px;
    line-height: 1.6;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.footer-section .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: background-color 0.3s;
}

.footer-section .social-links a:hover {
    background-color: var(--accent-color);
}

.footer-section .contact-info p {
    margin-bottom: 10px;
}

.footer-section .contact-info i {
    margin-right: 10px;
    color: var(--accent-color);
}

.footer-bottom {
    border-top: 1px solid #34495e;
    margin-top: 30px;
    padding-top: 20px;
    text-align: center;
    color: #bdc3c7;
}

/* ===== RESPONSIVE STYLES ===== */

@media (max-width: 768px) {
    .hero-title {
        font-size: 36px;
    }

    .hero-subtitle {
        font-size: 20px;
    }

    .hero-description {
        font-size: 16px;
    }

    .section-title {
        font-size: 28px;
    }

    .main-navigation .navbar-nav > li > a {
        padding: 12px 15px;
    }

    .search-box {
        margin: 20px 0;
    }

    .prayer-times-widget {
        margin-top: 20px;
    }

    .hero-section {
        padding: 50px 0;
    }

    .featured-content,
    .categories-section,
    .latest-content {
        padding: 50px 0;
    }
}

@media (max-width: 576px) {
    .hero-buttons .btn {
        display: block;
        margin-bottom: 15px;
        margin-right: 0;
    }

    .newsletter-form .form-control,
    .newsletter-form .btn {
        border-radius: 25px;
        margin-bottom: 10px;
    }
}

/* ===== HOVER EFFECTS VE INTERACTIVE ELEMENTS ===== */

/* Spotlight Card Hover Effects */
.spotlight-card {
    cursor: pointer;
}

.spotlight-card:hover .tefsir-gradient {
    background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
}

.spotlight-card:hover .hadis-gradient {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
}

.spotlight-card:hover .fikih-gradient {
    background: linear-gradient(135deg, #3498db, #2980b9) !important;
}

.spotlight-card:hover .siyer-gradient {
    background: linear-gradient(135deg, #f39c12, #e67e22) !important;
}

/* Button Hover Effects */
.btn-primary {
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(90, 108, 125, 0.3);
}

.btn-outline-primary {
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(90, 108, 125, 0.3);
}

/* Interactive Elements */
.header-actions a {
    transition: all 0.3s ease;
    cursor: pointer;
}

.header-actions a:hover {
    color: var(--pastel-blue) !important;
    transform: scale(1.1);
}

/* Scholar Follow Button */
.scholar-follow-btn {
    background: var(--pastel-blue);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    cursor: pointer;
}

.scholar-follow-btn:hover {
    background: #3498db;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}



/* ===== SPOTLIGHTS SECTION ===== */

.spotlights-section {
    background: #f8f9fa;
    padding: 40px 0;
}

.spotlights-section .section-title {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 600;
    text-align: left;
}

/* ===== VIEW ALL SECTION ===== */

.view-all-section {
    margin-top: 20px;
}

.view-all-btn {
    padding: 10px 30px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
}

/* ===== CONTENT FEED SECTION ===== */

.content-feed {
    padding: 40px 0;
}

/* ===== PROPORTIONAL RESPONSIVE DESIGN ===== */

/* Navigation optimization for all screen sizes - FORCE COMPACT */
.main-navigation .navbar-nav {
    display: flex !important;
    flex-wrap: nowrap !important;
    justify-content: flex-start !important;
    width: 100% !important;
    gap: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
}

.main-navigation .navbar-nav > li {
    flex: none !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
    display: inline-block !important;
    float: none !important;
}

.main-navigation .navbar-nav > li > a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

/* Container scales with screen width */
.container {
    padding: 0 calc(10px + 1vw);
}

.main-header {
    padding: calc(10px + 0.5vh) 0;
}

.search-box {
    margin-top: calc(8px + 0.5vh);
    max-width: calc(300px + 10vw);
}

/* Content spacing scales proportionally */
.spotlight-card {
    margin-bottom: calc(15px + 0.5vh);
}

.content-card {
    margin-bottom: calc(15px + 0.5vh);
}

.widget {
    margin-bottom: calc(15px + 0.5vh);
}

/* Mobile-specific adjustments only when needed */
@media (max-width: 768px) {
    /* Enable hamburger menu for mobile */
    .main-navigation .navbar-nav {
        flex-direction: column;
        width: 100%;
    }

    .main-navigation .navbar-nav > li {
        width: 100%;
        text-align: left;
    }

    /* Search box full width on mobile */
    .search-box {
        max-width: 100%;
    }
}
