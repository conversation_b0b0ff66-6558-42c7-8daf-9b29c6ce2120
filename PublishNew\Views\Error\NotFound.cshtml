@{
    ViewBag.Title = "<PERSON><PERSON> Bulunamadı - <PERSON><PERSON>";
    Layout = "/Views/Shared/_Layout.cshtml";
}

<div class="container" style="padding: 50px 0;">
    <div class="row">
        <div class="col-md-8 col-md-offset-2 text-center">
            <div class="error-page">
                <h1 style="font-size: 120px; color: #e74c3c; margin-bottom: 20px;">404</h1>
                <h2 style="color: #2c3e50; margin-bottom: 20px;">Sayfa Bulunamadı</h2>
                <p style="font-size: 18px; color: #7f8c8d; margin-bottom: 30px;">
                    Aradığınız sayfa bulunamadı. <PERSON><PERSON>şınmış, silinmiş veya geçici olarak erişilemez durumda olabilir.
                </p>
                
                <div class="error-actions" style="margin-top: 30px;">
                    <a href="/" class="btn btn-primary btn-lg" style="margin-right: 10px;">
                        <i class="fas fa-home"></i> Ana Sayfaya Dön
                    </a>
                    <a href="/Home/Contact" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-envelope"></i> İletişime Geç
                    </a>
                </div>
                
                <div style="margin-top: 40px;">
                    <h4 style="color: #2c3e50;">Popüler Sayfalar:</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0;"><a href="/Home/About">Hakkında</a></li>
                        <li style="margin: 10px 0;"><a href="/Home/Articles">Makaleler</a></li>
                        <li style="margin: 10px 0;"><a href="/Home/Videos">Videolar</a></li>
                        <li style="margin: 10px 0;"><a href="/Home/Books">Kitaplar</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
