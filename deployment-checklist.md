# PRODUCTION DEPLOYMENT CHECKLIST

## 🚨 **SUNUCU HATASI ÇÖZÜMLERİ**

### 1. **BIN KLASÖRÜ KONTROL EDİN**
Sunucuda şu DLL'lerin olduğundan emin olun:

**<PERSON><PERSON><PERSON><PERSON> DLL'ler:**
- ✅ YasinKaratas.Web.dll (Ana proje DLL'i)
- ✅ System.Web.Mvc.dll (5.2.9)
- ✅ System.Web.Optimization.dll
- ✅ System.Web.WebPages.dll
- ✅ System.Web.WebPages.Deployment.dll
- ✅ System.Web.WebPages.Razor.dll
- ✅ System.Web.Helpers.dll
- ✅ Microsoft.Web.Infrastructure.dll
- ✅ WebGrease.dll
- ✅ Antlr3.Runtime.dll
- ✅ EntityFramework.dll
- ✅ EntityFramework.SqlServer.dll
- ✅ Newtonsoft.Json.dll

### 2. **WEB.CONFIG KONTROL EDİN**
```xml
<!-- Debug modunda detaylı hatalar -->
<system.web>
  <customErrors mode="Off" />
  <compilation debug="true" targetFramework="4.8" />
  <trace enabled="true" />
</system.web>

<system.webServer>
  <httpErrors errorMode="Detailed" />
</system.webServer>
```

### 3. **CONNECTION STRING KONTROL EDİN**
Production sunucusunda connection string doğru mu?
```xml
<connectionStrings>
  <add name="DefaultConnection" 
       connectionString="Server=SUNUCU_ADI;Database=YasinKaratasWeb_Prod;User Id=KULLANICI;Password=*****;MultipleActiveResultSets=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 4. **IIS AYARLARI**
- **Application Pool**: .NET Framework v4.0
- **Managed Pipeline Mode**: Integrated
- **Target Framework**: 4.8
- **32-bit Applications**: False (64-bit)

### 5. **DOSYA İZİNLERİ**
- **IIS_IUSRS**: Read & Execute
- **Application Pool Identity**: Full Control (bin, temp klasörleri)

### 6. **YAYGIN HATALAR VE ÇÖZÜMLERİ**

#### **A) Assembly Load Hatası**
```
Could not load file or assembly 'System.Web.Mvc'
```
**Çözüm**: System.Web.Mvc.dll'i bin klasörüne kopyalayın

#### **B) Compilation Hatası**
```
CS0246: The type or namespace name 'Controller' could not be found
```
**Çözüm**: Tüm MVC DLL'lerini bin klasörüne kopyalayın

#### **C) Runtime Hatası**
```
Runtime Error: An exception occurred while processing your request
```
**Çözüm**: Web.config'te customErrors="Off" yapın

#### **D) Database Hatası**
```
Cannot open database requested by the login
```
**Çözüm**: Connection string'i kontrol edin

### 7. **DEPLOYMENT KOMUTU**
```powershell
# Tüm dosyaları sunucuya kopyala
xcopy /s /e /y "YasinKaratas.Web\*" "C:\inetpub\wwwroot\yasinkaratas\"

# Bin klasörünü özellikle kopyala
xcopy /s /e /y "YasinKaratas.Web\bin\*" "C:\inetpub\wwwroot\yasinkaratas\bin\"
```

### 8. **TEST ADIMLARI**
1. **Basit HTML sayfası test edin**: test.html
2. **Web.config syntax kontrol edin**
3. **Bin klasöründeki DLL'leri kontrol edin**
4. **IIS Application Pool'u restart edin**
5. **Event Viewer'da hata loglarını kontrol edin**

### 9. **HATA LOGLARI**
**Windows Event Viewer:**
- Windows Logs > Application
- Applications and Services Logs > IIS

**IIS Logs:**
- C:\inetpub\logs\LogFiles\W3SVC1\
