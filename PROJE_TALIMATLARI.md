# YASIN KARATAŞ HOCA WEB SİTESİ PROJE TALİMATLARI

## 🎯 GENEL PROJE TANIMI

Yasin Karataş Hoca için **yasinkaratas.com.tr** domain'ine sahip, İslami ilimler konularında eğitim veren bir hoca için resmi web sitesi geliştirilecek.

## 🛠️ TEKNİK GEREKSİNİMLER

### Teknoloji Stack:
- **Backend**: C# ASP.NET MVC Framework 4.8
- **Frontend**: HTML5, Bootstra<PERSON>, CSS3
- **Veritabanı**: Microsoft SQL Server (MSSQL)
- **ORM**: Entity Framework (EF)
- **Responsive**: Tüm cihazlarda uyumlu olacak
- **SEO**: Tam SEO uyumlu olacak

### Dil Desteği:
- **Türkçe (TR)** - Varsayılan dil
- **İngilizce (EN)** 
- **Arapça (AR)**
- Çok dil al<PERSON>uru<PERSON>, sonradan içerik eklenecek

## 🎨 TASARIM REFERANSİ

**Referans Site**: https://en.islamway.net/
- Bu siteden esinlenilecek
- Benzer layout ve yapı kullanılacak
- Modern, temiz ve kullanıcı dostu arayüz
- İslami temalara uygun renk paleti

## 📝 İÇERİK VE ADLANDIRMA KURALLARI

### Önemli Kural:
- Sitede sadece **"Yasin Karataş Hoca"** ifadesi kullanılacak
- "İslam alimi", "din bilgini" gibi ifadeler kullanılmayacak
- Tüm sayfa adresleri, değişkenler ve kod elementleri **İngilizce** olacak

### İçerik Kategorileri:
- **Dersler** (Lessons)
- **Makaleler** (Articles) 
- **Videolar** (Videos)
- **Kitaplar** (Books)
- **Sorular** (Questions/Fatawa)
- **Hakkında** (About)
- **İletişim** (Contact)

## 🗄️ VERİTABANI KONFIGÜRASYONU

### Ortam Bazlı Connection String'ler:

**Debug Ortamı:**
```
Data Source=(LocalDb)\MSSQLLocalDB;Initial Catalog=YasinKaratasWeb_Dev;Integrated Security=True
```

**Production Ortamı:**
```
Server=production-server;Database=YasinKaratasWeb_Prod;User Id=username;Password=password
```

### Gereksinimler:
- Debug ve Production için ayrı connection string'ler
- Web.Debug.config ve Web.Release.config dosyaları oluşturulacak
- Entity Framework 6.x kullanılacak

## 🏗️ PROJE YAPISI

### Klasör Yapısı:
```
YasinKaratas.Web/
├── Controllers/
│   └── HomeController.cs
├── Views/
│   ├── Shared/
│   │   └── _Layout.cshtml
│   └── Home/
│       └── Index.cshtml
├── Content/
│   └── Site.css
├── Scripts/
├── App_Start/
├── Models/
└── App_GlobalResources/ (çok dil için)
```

## 📄 OLUŞTURULACAK SAYFALAR

### 1. Ana Layout (_Layout.cshtml)
**Header Bölümü:**
- Logo alanı: "Yasin Karataş Hoca"
- Dil seçici (TR/EN/AR dropdown)
- Ana navigasyon menüsü
- Arama kutusu

**Navigasyon Menüsü:**
- Ana Sayfa (Home)
- Hakkında (About) 
- Dersler (Lessons) - Dropdown ile alt kategoriler
- Makaleler (Articles)
- Videolar (Videos)
- Kitaplar (Books)
- Sorular (Questions)
- İletişim (Contact)

**Footer Bölümü:**
- Sosyal medya linkleri
- Telif hakları
- Hızlı linkler
- İletişim bilgileri

### 2. Ana Sayfa (Index.cshtml)
**İçerik Bölümleri:**
- Hero/Banner alanı (Yasin Karataş Hoca tanıtımı)
- Son eklenen dersler
- Öne çıkan makaleler
- Kategori showcase'i
- Namaz vakitleri widget'ı (opsiyonel)
- Newsletter abonelik formu

## 🎨 TASARIM ÖZELLİKLERİ

### Renk Paleti:
- **Ana Renk**: İslami yeşil tonları (#2c5530, #4a7c59)
- **Vurgu Rengi**: Altın/sarı tonları (#d4af37)
- **Metin**: Koyu gri (#333)
- **Arka Plan**: Açık gri/beyaz tonları

### Typography:
- **Ana Font**: Roboto (Google Fonts)
- **Başlık Font**: Amiri (Arapça uyumlu, Google Fonts)
- Okunabilir ve modern fontlar

### Responsive Özellikler:
- Mobile-first yaklaşım
- Bootstrap grid sistemi
- Touch-friendly interface
- Tüm cihazlarda uyumlu

## 🔍 SEO GEREKSİNİMLERİ

### Meta Tags:
- Title, Description, Keywords
- Open Graph tags (Facebook)
- Twitter Card tags
- Canonical URL'ler

### Teknik SEO:
- Semantic HTML5 yapısı
- Schema.org markup hazırlığı
- Sitemap.xml hazırlığı
- Robots.txt
- URL yapısı: `/tr/`, `/en/`, `/ar/`

## 🌍 ÇOK DİL ALTYAPISI

### URL Yapısı:
- **Türkçe**: `www.yasinkaratas.com.tr/` (varsayılan)
- **İngilizce**: `www.yasinkaratas.com.tr/en/`
- **Arapça**: `www.yasinkaratas.com.tr/ar/`

### Teknik Gereksinimler:
- Resource dosyaları (.resx) hazırlığı
- Culture-based routing
- Dil değiştirme mekanizması
- Global.asax'ta culture ayarları

## 📱 RESPONSIVE BREAKPOINTS

- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px  
- **Mobile**: 576px - 767px
- **Small Mobile**: <576px

## 🔧 PROJE KURULUM ADIMLARI

### 1. Proje Oluşturma:
- ASP.NET MVC 4.8 projesi oluştur
- Solution dosyası (.sln) oluştur
- Gerekli klasör yapısını kur

### 2. Konfigürasyon:
- Web.config ayarları
- Web.Debug.config ve Web.Release.config
- RouteConfig.cs (çok dil routing)
- BundleConfig.cs (CSS/JS)

### 3. Layout ve Sayfalar:
- _Layout.cshtml (ana layout)
- Index.cshtml (ana sayfa)
- Error.cshtml (hata sayfası)

### 4. CSS ve JavaScript:
- Bootstrap 3.4.1 entegrasyonu
- Font Awesome icons
- Google Fonts
- Özel Site.css

### 5. Test ve Debug:
- IIS Express ile çalıştırma
- Hata ayıklama
- Responsive test

## 🚀 DEPLOYMENT GEREKSİNİMLERİ

### Production Optimizasyonları:
- HTTPS redirect
- Security headers
- Compression
- Caching
- Custom error pages

### Ortam Ayarları:
- Debug: Detaylı hatalar, trace enabled
- Production: Custom errors, compression, security

## 📞 İLETİŞİM BİLGİLERİ

- **Domain**: yasinkaratas.com.tr
- **Email**: <EMAIL>
- **Sosyal Medya**: Facebook, Twitter, YouTube, Instagram linkleri

## ⚠️ ÖNEMLİ NOTLAR

1. **Sadelik**: Kod tarafı sade ve temiz olacak
2. **Performans**: Hızlı yüklenen, optimize edilmiş
3. **Güvenlik**: XSS, CSRF koruması
4. **Erişilebilirlik**: WCAG uyumlu
5. **Bakım**: Kolay güncellenebilir yapı

Bu talimatlar doğrultusunda, Yasin Karataş Hoca için profesyonel, modern ve İslami değerlere uygun bir web sitesi geliştirilecektir.
