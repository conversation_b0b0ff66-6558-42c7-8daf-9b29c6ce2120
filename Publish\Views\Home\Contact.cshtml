@{
    ViewBag.Title = "İlet<PERSON><PERSON><PERSON> - <PERSON><PERSON>";
    ViewBag.MetaDescription = "<PERSON><PERSON> ile iletişime geçin. Sorularınız, önerileriniz ve ders talepleriniz için bizimle iletişime geçebilirsiniz.";
    ViewBag.MetaKeywords = "<PERSON><PERSON>, iletişim, contact, email, telefon";
}

<!-- Page Header -->
<section class="page-header" style="background: linear-gradient(135deg, #5a6c7d 0%, #7a8a9a 100%); color: white; padding: 80px 0;">
    <div class="container">
        <div class="row">
            <div class="col-md-12 text-center">
                <h1 style="font-size: 48px; margin-bottom: 15px; font-weight: 700;"><PERSON>letişim</h1>
                <p style="font-size: 20px; opacity: 0.9; margin: 0;">Bizimle iletişime geçin</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Content -->
<section class="contact-content" style="padding: 80px 0;">
    <div class="container">
        <div class="row">
            <!-- Contact Info -->
            <div class="col-md-6">
                <div class="contact-info-section">
                    <h2 style="color: #2c3e50; margin-bottom: 30px; font-weight: 600;">İletişim Bilgileri</h2>
                    
                    <div class="contact-item" style="display: flex; align-items: center; margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <div class="contact-icon" style="width: 50px; height: 50px; background: #8fa4b3; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                            <i class="fas fa-envelope" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <h5 style="margin: 0; color: #2c3e50; font-weight: 600;">Email</h5>
                            <a href="mailto:<EMAIL>" style="color: #8fa4b3; text-decoration: none; font-size: 16px;"><EMAIL></a>
                        </div>
                    </div>
                    
                    <div class="contact-item" style="display: flex; align-items: center; margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <div class="contact-icon" style="width: 50px; height: 50px; background: #9bb5a2; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                            <i class="fas fa-globe" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <h5 style="margin: 0; color: #2c3e50; font-weight: 600;">Website</h5>
                            <a href="https://www.yasinkaratas.com.tr" style="color: #9bb5a2; text-decoration: none; font-size: 16px;">www.yasinkaratas.com.tr</a>
                        </div>
                    </div>
                    
                    <div class="contact-item" style="display: flex; align-items: center; margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <div class="contact-icon" style="width: 50px; height: 50px; background: #c4a484; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px;">
                            <i class="fas fa-clock" style="color: white; font-size: 20px;"></i>
                        </div>
                        <div>
                            <h5 style="margin: 0; color: #2c3e50; font-weight: 600;">Çalışma Saatleri</h5>
                            <p style="margin: 0; color: #7f8c8d; font-size: 16px;">Pazartesi - Cuma: 09:00 - 18:00</p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="social-media-section" style="margin-top: 40px;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px; font-weight: 600;">Sosyal Medya</h3>
                    <div class="social-links" style="display: flex; gap: 15px;">
                        <a href="#" style="width: 50px; height: 50px; background: #3b5998; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: transform 0.3s;">
                            <i class="fab fa-facebook-f" style="color: white; font-size: 20px;"></i>
                        </a>
                        <a href="#" style="width: 50px; height: 50px; background: #1da1f2; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: transform 0.3s;">
                            <i class="fab fa-twitter" style="color: white; font-size: 20px;"></i>
                        </a>
                        <a href="#" style="width: 50px; height: 50px; background: #ff0000; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: transform 0.3s;">
                            <i class="fab fa-youtube" style="color: white; font-size: 20px;"></i>
                        </a>
                        <a href="#" style="width: 50px; height: 50px; background: #e4405f; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: transform 0.3s;">
                            <i class="fab fa-instagram" style="color: white; font-size: 20px;"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="col-md-6">
                <div class="contact-form-section">
                    <h2 style="color: #2c3e50; margin-bottom: 30px; font-weight: 600;">Mesaj Gönderin</h2>

                    @if (!string.IsNullOrEmpty(ViewBag.SuccessMessage))
                    {
                        <div class="alert alert-success" style="background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #c3e6cb;">
                            <i class="fas fa-check-circle"></i> @ViewBag.SuccessMessage
                        </div>
                    }

                    <form action="/Home/Contact" method="post" class="contact-form" style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                        <div class="form-group" style="margin-bottom: 20px;">
                            <label for="name" style="color: #2c3e50; font-weight: 500; margin-bottom: 8px; display: block;">Ad Soyad *</label>
                            <input type="text" id="name" name="name" class="form-control" required style="border: 2px solid #ecf0f1; border-radius: 8px; padding: 12px 15px; font-size: 14px; transition: border-color 0.3s;">
                        </div>

                        <div class="form-group" style="margin-bottom: 20px;">
                            <label for="email" style="color: #2c3e50; font-weight: 500; margin-bottom: 8px; display: block;">Email *</label>
                            <input type="email" id="email" name="email" class="form-control" required style="border: 2px solid #ecf0f1; border-radius: 8px; padding: 12px 15px; font-size: 14px; transition: border-color 0.3s;">
                        </div>

                        <div class="form-group" style="margin-bottom: 20px;">
                            <label for="subject" style="color: #2c3e50; font-weight: 500; margin-bottom: 8px; display: block;">Konu</label>
                            <input type="text" id="subject" name="subject" class="form-control" style="border: 2px solid #ecf0f1; border-radius: 8px; padding: 12px 15px; font-size: 14px; transition: border-color 0.3s;">
                        </div>

                        <div class="form-group" style="margin-bottom: 25px;">
                            <label for="message" style="color: #2c3e50; font-weight: 500; margin-bottom: 8px; display: block;">Mesajınız *</label>
                            <textarea id="message" name="message" rows="5" class="form-control" required style="border: 2px solid #ecf0f1; border-radius: 8px; padding: 12px 15px; font-size: 14px; transition: border-color 0.3s; resize: vertical;"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary btn-lg" style="background: #8fa4b3; border: none; padding: 12px 30px; border-radius: 8px; font-weight: 600; transition: background 0.3s; width: 100%;">
                            <i class="fas fa-paper-plane"></i> Mesaj Gönder
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section" style="background: #f8f9fa; padding: 80px 0;">
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 50px; font-weight: 600;">Sıkça Sorulan Sorular</h2>
                
                <div class="faq-item" style="background: white; margin-bottom: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <div class="faq-question" style="padding: 20px; background: #8fa4b3; color: white; cursor: pointer;">
                        <h5 style="margin: 0; font-weight: 600;">
                            <i class="fas fa-question-circle"></i> Ders programı nasıl öğrenebilirim?
                        </h5>
                    </div>
                    <div class="faq-answer" style="padding: 20px; color: #555;">
                        <p style="margin: 0;">Ders programımızı web sitemizin "Dersler" bölümünden takip edebilir, email ile bizimle iletişime geçerek detaylı bilgi alabilirsiniz.</p>
                    </div>
                </div>
                
                <div class="faq-item" style="background: white; margin-bottom: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <div class="faq-question" style="padding: 20px; background: #9bb5a2; color: white; cursor: pointer;">
                        <h5 style="margin: 0; font-weight: 600;">
                            <i class="fas fa-question-circle"></i> Online dersler var mı?
                        </h5>
                    </div>
                    <div class="faq-answer" style="padding: 20px; color: #555;">
                        <p style="margin: 0;">Evet, online tefsir ve hadis derslerimiz bulunmaktadır. Video dersler bölümünden erişebilir veya canlı derslere katılabilirsiniz.</p>
                    </div>
                </div>
                
                <div class="faq-item" style="background: white; margin-bottom: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
                    <div class="faq-question" style="padding: 20px; background: #c4a484; color: white; cursor: pointer;">
                        <h5 style="margin: 0; font-weight: 600;">
                            <i class="fas fa-question-circle"></i> Kitap önerileri alabilir miyim?
                        </h5>
                    </div>
                    <div class="faq-answer" style="padding: 20px; color: #555;">
                        <p style="margin: 0;">Tabii ki! İlgi alanınıza göre kitap önerileri için bizimle iletişime geçebilir, "Kitaplar" bölümünden önerilen eserleri inceleyebilirsiniz.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.contact-form input:focus,
.contact-form textarea:focus {
    border-color: #8fa4b3 !important;
    outline: none;
    box-shadow: 0 0 0 3px rgba(143, 164, 179, 0.1);
}

.btn-primary:hover {
    background: #7a8a9a !important;
    transform: translateY(-2px);
}

.social-links a:hover {
    transform: scale(1.1);
}

.faq-question:hover {
    opacity: 0.9;
}

@@media (max-width: 768px) {
    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-right: 0 !important;
        margin-bottom: 15px;
    }

    .social-links {
        justify-content: center;
    }
}
</style>

<script>
// FAQ Accordion
document.addEventListener('DOMContentLoaded', function() {
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const isOpen = answer.style.display === 'block';
            
            // Tüm cevapları kapat
            document.querySelectorAll('.faq-answer').forEach(ans => {
                ans.style.display = 'none';
            });
            
            // Tıklanan sorunun cevabını aç/kapat
            if (!isOpen) {
                answer.style.display = 'block';
            }
        });
    });
    
    // Form artık HTML5 validation kullanıyor
});
</script>
