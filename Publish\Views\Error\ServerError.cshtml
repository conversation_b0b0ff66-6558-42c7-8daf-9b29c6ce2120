@{
    ViewBag.Title = "<PERSON>uc<PERSON><PERSON>";
    Layout = "/Views/Shared/_Layout.cshtml";
}

<div class="container" style="padding: 50px 0;">
    <div class="row">
        <div class="col-md-8 col-md-offset-2 text-center">
            <div class="error-page">
                <h1 style="font-size: 120px; color: #e74c3c; margin-bottom: 20px;">500</h1>
                <h2 style="color: #2c3e50; margin-bottom: 20px;">Sunucu Hatası</h2>
                <p style="font-size: 18px; color: #7f8c8d; margin-bottom: 30px;">
                    Üzgünüz, sunucuda bir hata oluştu. Teknik ekibimiz bu sorunu çözmek için çalışıyor.
                    Lütfen daha sonra tekrar deneyin.
                </p>
                
                <div class="error-actions" style="margin-top: 30px;">
                    <a href="/" class="btn btn-primary btn-lg" style="margin-right: 10px;">
                        <i class="fas fa-home"></i> Ana Sayfaya Dön
                    </a>
                    <a href="javascript:history.back()" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-arrow-left"></i> Geri Dön
                    </a>
                </div>
                
                <div style="margin-top: 40px;">
                    <h4 style="color: #2c3e50;">Bu sorunu çözmek için:</h4>
                    <ul style="list-style: none; padding: 0; color: #7f8c8d;">
                        <li style="margin: 10px 0;">• Sayfayı yenileyin (F5)</li>
                        <li style="margin: 10px 0;">• Birkaç dakika sonra tekrar deneyin</li>
                        <li style="margin: 10px 0;">• Sorun devam ederse bizimle iletişime geçin</li>
                    </ul>
                </div>
                
                <div style="margin-top: 30px;">
                    <p style="color: #95a5a6;">
                        <i class="fas fa-envelope"></i> 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
