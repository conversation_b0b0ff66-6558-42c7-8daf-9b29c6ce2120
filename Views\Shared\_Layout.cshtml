<!DOCTYPE html>
<html lang="@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName)">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="@ViewBag.MetaDescription" />
    <meta name="keywords" content="@ViewBag.MetaKeywords" />
    <meta name="author" content="Yasin Karataş Hoca" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@ViewBag.Title" />
    <meta property="og:description" content="@ViewBag.MetaDescription" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="@Request.Url.AbsoluteUri" />
    <meta property="og:site_name" content="<PERSON><PERSON>" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="@ViewBag.Title" />
    <meta name="twitter:description" content="@ViewBag.MetaDescription" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="@Request.Url.AbsoluteUri" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="~/Content/Site.css" rel="stylesheet" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span><i class="fas fa-envelope"></i> <EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="header-right">
                            <!-- Language Selector -->
                            <div class="language-selector">
                                <div class="dropdown">
                                    <button class="btn btn-sm dropdown-toggle" type="button" data-toggle="dropdown">
                                        @{
                                            var currentLang = System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;
                                            var langText = currentLang == "tr" ? "Türkçe" : currentLang == "en" ? "English" : "العربية";
                                        }
                                        <i class="fas fa-globe"></i> @langText
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a href="@Url.Action("Index", "Home", new { language = "tr" })">Türkçe</a></li>
                                        <li><a href="@Url.Action("Index", "Home", new { language = "en" })">English</a></li>
                                        <li><a href="@Url.Action("Index", "Home", new { language = "ar" })">العربية</a></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Social Media -->
                            <div class="social-links">
                                <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                                <a href="#" title="YouTube"><i class="fab fa-youtube"></i></a>
                                <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Header -->
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <!-- Logo -->
                        <div class="site-logo">
                            <a href="@Url.Action("Index", "Home")">
                                <h2 class="logo-text">Yasin Karataş Hoca</h2>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- Search Box -->
                        <div class="search-box">
                            <form class="search-form" method="get" action="@Url.Action("Search", "Home")">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="q" placeholder="Ara..." />
                                    <div class="input-group-addon">
                                        <button class="btn btn-search" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <!-- Prayer Times Widget -->
                        <div class="prayer-times-widget">
                            <div class="prayer-time">
                                <span class="prayer-name">Namaz Vakti</span>
                                <span class="prayer-clock" id="prayer-clock">--:--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <nav class="main-navigation">
            <div class="container">
                <div class="navbar navbar-default">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                    </div>
                    <div class="collapse navbar-collapse" id="navbar-collapse">
                        <ul class="nav navbar-nav">
                            <li>
                                <a href="@Url.Action("Index", "Home")">
                                    <i class="fas fa-home"></i> Ana Sayfa
                                </a>
                            </li>
                            <li>
                                <a href="@Url.Action("About", "Home")">
                                    <i class="fas fa-user"></i> Hakkında
                                </a>
                            </li>
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <i class="fas fa-book"></i> Dersler <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a href="#">Tefsir Dersleri</a></li>
                                    <li><a href="#">Hadis Dersleri</a></li>
                                    <li><a href="#">Fıkıh Dersleri</a></li>
                                    <li><a href="#">Siyer Dersleri</a></li>
                                </ul>
                            </li>
                            <li>
                                <a href="@Url.Action("Articles", "Home")">
                                    <i class="fas fa-newspaper"></i> Makaleler
                                </a>
                            </li>
                            <li>
                                <a href="@Url.Action("Videos", "Home")">
                                    <i class="fas fa-video"></i> Videolar
                                </a>
                            </li>
                            <li>
                                <a href="@Url.Action("Books", "Home")">
                                    <i class="fas fa-book-open"></i> Kitaplar
                                </a>
                            </li>
                            <li>
                                <a href="#">
                                    <i class="fas fa-question-circle"></i> Sorular
                                </a>
                            </li>
                            <li>
                                <a href="@Url.Action("Contact", "Home")">
                                    <i class="fas fa-envelope"></i> İletişim
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>Yasin Karataş Hoca</h5>
                        <p>İslami ilimler alanında eğitim ve öğretim faaliyetleri.</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>Hızlı Linkler</h5>
                        <ul class="footer-links">
                            <li><a href="@Url.Action("Index", "Home")">Ana Sayfa</a></li>
                            <li><a href="@Url.Action("About", "Home")">Hakkında</a></li>
                            <li><a href="@Url.Action("Lessons", "Home")">Dersler</a></li>
                            <li><a href="@Url.Action("Articles", "Home")">Makaleler</a></li>
                            <li><a href="@Url.Action("Contact", "Home")">İletişim</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>İletişim</h5>
                        <div class="contact-info">
                            <p><i class="fas fa-envelope"></i> <EMAIL></p>
                            <p><i class="fas fa-globe"></i> www.yasinkaratas.com.tr</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <p>&copy; @DateTime.Now.Year Yasin Karataş Hoca. Tüm hakları saklıdır.</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    @RenderSection("scripts", required: false)
    
    <script>
        // Prayer times clock (placeholder)
        function updatePrayerClock() {
            var now = new Date();
            var timeString = now.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
            document.getElementById('prayer-clock').textContent = timeString;
        }
        
        setInterval(updatePrayerClock, 1000);
        updatePrayerClock();
    </script>
</body>
</html>
