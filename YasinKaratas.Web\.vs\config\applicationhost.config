<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <configSections>
    <sectionGroup name="system.applicationHost">
      <section name="applicationInitialization" overrideModeDefault="Allow" />
      <section name="customMetadata" overrideModeDefault="Deny" />
      <section name="listenerAdapters" overrideModeDefault="Deny" />
      <section name="log" overrideModeDefault="Deny" />
      <section name="sites" overrideModeDefault="Deny" />
      <section name="webLimits" overrideModeDefault="Deny" />
    </sectionGroup>
    <sectionGroup name="system.webServer">
      <section name="asp" overrideModeDefault="Deny" />
      <section name="caching" overrideModeDefault="Allow" />
      <section name="cgi" overrideModeDefault="Deny" />
      <section name="defaultDocument" overrideModeDefault="Allow" />
      <section name="directoryBrowse" overrideModeDefault="Allow" />
      <section name="fastCgi" overrideModeDefault="Deny" />
      <section name="globalModules" overrideModeDefault="Deny" />
      <section name="handlers" overrideModeDefault="Allow" />
      <section name="httpCompression" overrideModeDefault="Allow" />
      <section name="httpErrors" overrideModeDefault="Allow" />
      <section name="httpLogging" overrideModeDefault="Deny" />
      <section name="httpProtocol" overrideModeDefault="Allow" />
      <section name="httpRedirect" overrideModeDefault="Allow" />
      <section name="httpTracing" overrideModeDefault="Deny" />
      <section name="isapiFilters" overrideModeDefault="Deny" />
      <section name="modules" overrideModeDefault="Allow" />
      <section name="odbcLogging" overrideModeDefault="Deny" />
      <section name="security" overrideModeDefault="Allow" />
      <section name="serverRuntime" overrideModeDefault="Deny" />
      <section name="serverSideInclude" overrideModeDefault="Deny" />
      <section name="staticContent" overrideModeDefault="Allow" />
      <section name="tracing" overrideModeDefault="Deny" />
      <section name="urlCompression" overrideModeDefault="Allow" />
      <section name="validation" overrideModeDefault="Allow" />
    </sectionGroup>
  </configSections>
  
  <system.applicationHost>
    <sites>
      <site name="YasinKaratas.Web" id="1" serverAutoStart="true">
        <application path="/">
          <virtualDirectory path="/" physicalPath="D:\Yasin\yasinkaratas.com.tr\YasinKaratas.Web" />
        </application>
        <bindings>
          <binding protocol="http" bindingInformation="*:44300:localhost" />
          <binding protocol="https" bindingInformation="*:44301:localhost" />
        </bindings>
      </site>
    </sites>
  </system.applicationHost>
  
  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.htm" />
        <add value="Default.asp" />
        <add value="index.htm" />
        <add value="index.html" />
        <add value="iisstart.htm" />
        <add value="default.aspx" />
      </files>
    </defaultDocument>
    
    <modules>
      <add name="UrlRoutingModule-4.0" type="System.Web.Routing.UrlRoutingModule" preCondition="managedHandler,runtimeVersionv4.0" />
    </modules>
    
    <handlers>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
  </system.webServer>
</configuration>
