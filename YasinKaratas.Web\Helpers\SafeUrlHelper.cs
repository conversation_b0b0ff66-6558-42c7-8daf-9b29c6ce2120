using System;
using System.Web;
using System.Web.Mvc;

namespace YasinKaratas.Web.Helpers
{
    /// <summary>
    /// Medium Trust uyumlu URL helper
    /// </summary>
    public static class SafeUrlHelper
    {
        /// <summary>
        /// Medium Trust uyumlu Action URL oluşturur
        /// </summary>
        public static string SafeAction(this UrlHelper urlHelper, string actionName, string controllerName = null)
        {
            try
            {
                // Normal Url.Action kullanmayı dene
                return urlHelper.Action(actionName, controllerName);
            }
            catch (System.Security.SecurityException)
            {
                // Medium Trust'ta güvenlik hatası alırsa, manuel URL oluştur
                return CreateManualUrl(actionName, controllerName);
            }
            catch (Exception)
            {
                // Di<PERSON>er hatalar için de manuel URL
                return CreateManualUrl(actionName, controllerName);
            }
        }

        /// <summary>
        /// Manuel URL oluşturur (Medium Trust uyumlu)
        /// </summary>
        private static string CreateManualUrl(string actionName, string controllerName = null)
        {
            var baseUrl = "/";
            if (HttpContext.Current != null && HttpContext.Current.Request != null)
            {
                baseUrl = HttpContext.Current.Request.ApplicationPath ?? "/";
            }

            if (!baseUrl.EndsWith("/"))
                baseUrl += "/";

            if (string.IsNullOrEmpty(controllerName))
                controllerName = "Home";

            if (string.IsNullOrEmpty(actionName))
                actionName = "Index";

            // Manuel URL oluştur: /Controller/Action
            return baseUrl + controllerName + "/" + actionName;
        }

        /// <summary>
        /// Ana sayfa URL'i
        /// </summary>
        public static string HomeUrl()
        {
            return CreateManualUrl("Index", "Home");
        }

        /// <summary>
        /// Hakkında sayfası URL'i
        /// </summary>
        public static string AboutUrl()
        {
            return CreateManualUrl("About", "Home");
        }

        /// <summary>
        /// İletişim sayfası URL'i
        /// </summary>
        public static string ContactUrl()
        {
            return CreateManualUrl("Contact", "Home");
        }

        /// <summary>
        /// Makaleler sayfası URL'i
        /// </summary>
        public static string ArticlesUrl()
        {
            return CreateManualUrl("Articles", "Home");
        }

        /// <summary>
        /// Videolar sayfası URL'i
        /// </summary>
        public static string VideosUrl()
        {
            return CreateManualUrl("Videos", "Home");
        }

        /// <summary>
        /// Kitaplar sayfası URL'i
        /// </summary>
        public static string BooksUrl()
        {
            return CreateManualUrl("Books", "Home");
        }

        /// <summary>
        /// Dil değiştirme URL'i
        /// </summary>
        public static string LanguageUrl(string language)
        {
            var baseUrl = "/";
            if (HttpContext.Current != null && HttpContext.Current.Request != null)
            {
                baseUrl = HttpContext.Current.Request.ApplicationPath ?? "/";
            }

            if (!baseUrl.EndsWith("/"))
                baseUrl += "/";

            return baseUrl + "Home/Index?language=" + language;
        }

        /// <summary>
        /// Arama URL'i
        /// </summary>
        public static string SearchUrl()
        {
            return CreateManualUrl("Search", "Home");
        }
    }
}
