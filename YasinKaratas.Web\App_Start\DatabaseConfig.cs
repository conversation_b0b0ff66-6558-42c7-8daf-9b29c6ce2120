using System;
using System.Configuration;
using System.Data.Entity;

namespace YasinKaratas.Web
{
    /// <summary>
    /// Veritabanı konfigürasyon ayarları
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// Veritabanı bağlantı ayarlarını yapılandırır
        /// </summary>
        public static void Initialize()
        {
            // Entity Framework için database initializer ayarla
            Database.SetInitializer<YasinKaratasContext>(new CreateDatabaseIfNotExists<YasinKaratasContext>());
            
            // Connection string'i kontrol et
            var connectionString = GetConnectionString();
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Veritabanı bağlantı dizesi bulunamadı!");
            }
        }

        /// <summary>
        /// Aktif connection string'i döndürür
        /// </summary>
        /// <returns>Connection string</returns>
        public static string GetConnectionString()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["YasinKaratasContext"]?.ConnectionString;
            
            if (string.IsNullOrEmpty(connectionString))
            {
                connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
            }
            
            return connectionString;
        }

        /// <summary>
        /// Mevcut ortamı döndürür (Debug, Staging, Production)
        /// </summary>
        /// <returns>Ortam adı</returns>
        public static string GetEnvironment()
        {
#if DEBUG
            return "Debug";
#else
            var environment = ConfigurationManager.AppSettings["Environment"];
            return !string.IsNullOrEmpty(environment) ? environment : "Production";
#endif
        }

        /// <summary>
        /// Debug modunda mı kontrol eder
        /// </summary>
        /// <returns>Debug modunda ise true</returns>
        public static bool IsDebugMode()
        {
#if DEBUG
            return true;
#else
            return false;
#endif
        }

        /// <summary>
        /// Production modunda mı kontrol eder
        /// </summary>
        /// <returns>Production modunda ise true</returns>
        public static bool IsProductionMode()
        {
            return GetEnvironment().Equals("Production", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Staging modunda mı kontrol eder
        /// </summary>
        /// <returns>Staging modunda ise true</returns>
        public static bool IsStagingMode()
        {
            return GetEnvironment().Equals("Staging", StringComparison.OrdinalIgnoreCase);
        }
    }

    /// <summary>
    /// Entity Framework DbContext placeholder
    /// </summary>
    public class YasinKaratasContext : DbContext
    {
        public YasinKaratasContext() : base("YasinKaratasContext")
        {
            // Configuration
        }

        public YasinKaratasContext(string connectionString) : base(connectionString)
        {
            // Configuration
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // Model konfigürasyonları buraya eklenecek
            base.OnModelCreating(modelBuilder);
        }
    }
}
