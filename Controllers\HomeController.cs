using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace YasinKaratas.Web.Controllers
{
    public class HomeController : Controller
    {
        public ActionResult Index()
        {
            ViewBag.Title = "Yasin <PERSON> Ho<PERSON> - Ana Sayfa";
            ViewBag.MetaDescription = "Yasin <PERSON> Ho<PERSON>'nın resmi web sitesi. İslami il<PERSON>, dersler, makaleler ve daha fazlası.";
            ViewBag.MetaKeywords = "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, din dersleri, İslam, hoca, tefsir, hadis, fıkıh";
            
            return View();
        }

        public ActionResult About()
        {
            ViewBag.Title = "Hakkında - Yasin <PERSON> Ho<PERSON>";
            ViewBag.MetaDescription = "Yasin Kara<PERSON>ş Ho<PERSON> hakkında bilgiler, eğitim hayatı ve çalışmaları.";
            ViewBag.MetaKeywords = "Yasin <PERSON>, hakkında, biyografi, eğitim";
            
            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Title = "İletişim - <PERSON><PERSON>";
            ViewBag.MetaDescription = "Yasin Kara<PERSON>ş Ho<PERSON> ile iletişime geçin.";
            ViewBag.MetaKeywords = "Yasin Karataş, iletişim, contact";
            
            return View();
        }

        public ActionResult Lessons()
        {
            ViewBag.Title = "Dersler - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın tefsir, hadis, fıkıh ve siyer dersleri.";
            ViewBag.MetaKeywords = "dersler, tefsir, hadis, fıkıh, siyer";
            
            return View();
        }

        public ActionResult Articles()
        {
            ViewBag.Title = "Makaleler - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın İslami konulardaki makaleleri.";
            ViewBag.MetaKeywords = "makaleler, İslami yazılar, din";
            
            return View();
        }

        public ActionResult Videos()
        {
            ViewBag.Title = "Videolar - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın video dersleri ve konuşmaları.";
            ViewBag.MetaKeywords = "videolar, video dersler, konuşmalar";
            
            return View();
        }

        public ActionResult Books()
        {
            ViewBag.Title = "Kitaplar - Yasin Karataş Hoca";
            ViewBag.MetaDescription = "Yasin Karataş Hoca'nın kitapları ve önerilen eserler.";
            ViewBag.MetaKeywords = "kitaplar, eserler, İslami kitaplar";
            
            return View();
        }
    }
}
