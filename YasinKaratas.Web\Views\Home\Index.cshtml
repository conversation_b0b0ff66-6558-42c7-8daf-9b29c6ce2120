@{
    ViewBag.Title = "<PERSON><PERSON> - Ana <PERSON>";
    ViewBag.MetaDescription = "Ya<PERSON>'nın resmi web sitesi. <PERSON><PERSON><PERSON>, der<PERSON>, makaleler ve daha fazlası.";
    ViewBag.MetaKeywords = "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, din <PERSON>, <PERSON><PERSON><PERSON>, ho<PERSON>, tefs<PERSON>, hadis, fık<PERSON>h";
}

<!-- Spotlights Section (İslamway tarzı) -->
<section class="spotlights-section">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <h2 class="section-title"><PERSON>ne Çıkan <PERSON></h2>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="spotlight-card">
                    <div class="spotlight-image tefsir-gradient">
                        <div class="spotlight-icon-container">
                            <i class="fas fa-book-open"></i>
                            <h4>Tefsir</h4>
                        </div>
                    </div>
                    <div class="spotlight-content">
                        <h5><PERSON><PERSON>'<PERSON><PERSON><PERSON>rim Te<PERSON>iri</h5>
                        <p><PERSON><PERSON>'<PERSON>-<PERSON>rim'in anlamı ve yorumu üzerine detaylı dersler.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="spotlight-card">
                    <div class="spotlight-image hadis-gradient">
                        <div class="spotlight-icon-container">
                            <i class="fas fa-scroll"></i>
                            <h4>Hadis</h4>
                        </div>
                    </div>
                    <div class="spotlight-content">
                        <h5>Hadis-i Şerif Dersleri</h5>
                        <p>Peygamber Efendimizin sünnetleri ve hadisleri.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="spotlight-card">
                    <div class="spotlight-image fikih-gradient">
                        <div class="spotlight-icon-container">
                            <i class="fas fa-balance-scale"></i>
                            <h4>Fıkıh</h4>
                        </div>
                    </div>
                    <div class="spotlight-content">
                        <h5>İslam Hukuku</h5>
                        <p>İbadet ve muamelat konularında fıkhi hükümler.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="spotlight-card">
                    <div class="spotlight-image siyer-gradient">
                        <div class="spotlight-icon-container">
                            <i class="fas fa-heart"></i>
                            <h4>Siyer</h4>
                        </div>
                    </div>
                    <div class="spotlight-content">
                        <h5>Peygamber Efendimizin Hayatı</h5>
                        <p>Hz. Muhammed'in (SAV) hayatı ve örnekliği.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center view-all-section">
            <a href="/Home/Lessons" class="btn btn-primary view-all-btn">Tüm Dersleri Görüntüle</a>
        </div>
    </div>
</section>

<!-- Content Feed (İslamway tarzı) -->
<section class="content-feed" style="padding: 40px 0;">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Audio Recitation Card -->
                <div class="content-card" style="background: white; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                    <div class="card-header" style="padding: 15px; border-bottom: 1px solid #ecf0f1;">
                        <div class="author-info" style="display: flex; align-items: center;">
                            <div class="author-avatar" style="width: 40px; height: 40px; background: #8fa4b3; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                <i class="fas fa-user" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div>
                                <h6 style="margin: 0; color: #2c3e50; font-weight: 600;">Yasin Karataş Hoca</h6>
                                <small style="color: #7f8c8d;">Tefsir Dersleri - Hafs Rivayeti</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-content" style="padding: 20px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">Sûrat Al-Fatiha (Açılış)</h5>
                        <div class="audio-player" style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; justify-content: space-between;">
                                <div style="display: flex; align-items: center;">
                                    <button class="play-btn" style="background: #8fa4b3; color: white; border: none; border-radius: 50%; width: 40px; height: 40px; margin-right: 15px; transition: background 0.3s;">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <div>
                                        <div style="color: #2c3e50; font-weight: 600;">Fatiha Suresi</div>
                                        <div style="color: #7f8c8d; font-size: 14px;">Süre: 2:15</div>
                                    </div>
                                </div>
                                <div class="audio-controls">
                                    <button style="background: none; border: none; color: #7f8c8d; margin: 0 5px;"><i class="fas fa-heart"></i></button>
                                    <button style="background: none; border: none; color: #7f8c8d; margin: 0 5px;"><i class="fas fa-share"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="stats" style="display: flex; justify-content: space-between; color: #7f8c8d; font-size: 14px;">
                            <span><i class="fas fa-eye"></i> 1,245 görüntülenme</span>
                            <span><i class="fas fa-heart"></i> 89 beğeni</span>
                        </div>
                    </div>
                </div>

                <!-- Article Card -->
                <div class="content-card" style="background: white; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                    <div class="card-header" style="padding: 15px; border-bottom: 1px solid #ecf0f1;">
                        <div class="author-info" style="display: flex; align-items: center;">
                            <div class="author-avatar" style="width: 40px; height: 40px; background: #b89090; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                <i class="fas fa-pen" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div>
                                <h6 style="margin: 0; color: #2c3e50; font-weight: 600;">Yasin Karataş Hoca</h6>
                                <small style="color: #7f8c8d;">Makaleler - 2 gün önce</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-content" style="padding: 20px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">İmanın Gerçek Anlamı</h5>
                        <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
                            "Hiçbiriniz, kardeşi için sevdiğini kendisi için sevmedikçe gerçek anlamda iman etmiş olmaz." 
                            Bu hadis-i şerifin ışığında iman kavramını ele alıyoruz...
                        </p>
                        <div class="article-tags" style="margin-bottom: 15px;">
                            <span style="background: #ecf0f1; color: #7f8c8d; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-right: 5px;">#iman</span>
                            <span style="background: #ecf0f1; color: #7f8c8d; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-right: 5px;">#kardeşlik</span>
                        </div>
                        <div class="stats" style="display: flex; justify-content: space-between; color: #7f8c8d; font-size: 14px;">
                            <span><i class="fas fa-eye"></i> 2,156 okuma</span>
                            <span><i class="fas fa-heart"></i> 134 beğeni</span>
                        </div>
                    </div>
                </div>

                <!-- Video Card -->
                <div class="content-card" style="background: white; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden;">
                    <div class="card-header" style="padding: 15px; border-bottom: 1px solid #ecf0f1;">
                        <div class="author-info" style="display: flex; align-items: center;">
                            <div class="author-avatar" style="width: 40px; height: 40px; background: #c4a484; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                <i class="fas fa-video" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div>
                                <h6 style="margin: 0; color: #2c3e50; font-weight: 600;">Yasin Karataş Hoca</h6>
                                <small style="color: #7f8c8d;">Video Dersler - 1 hafta önce</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-content" style="padding: 20px;">
                        <h5 style="color: #2c3e50; margin-bottom: 15px;">Namaz ve Manevi Boyutu</h5>
                        <div class="video-thumbnail" style="background: #ecf0f1; height: 200px; border-radius: 6px; margin-bottom: 15px; position: relative; display: flex; align-items: center; justify-content: center;">
                            <div style="text-align: center;">
                                <i class="fas fa-play-circle" style="font-size: 48px; color: #3498db; margin-bottom: 10px;"></i>
                                <div style="color: #7f8c8d;">Video Süresi: 45:30</div>
                            </div>
                        </div>
                        <div class="stats" style="display: flex; justify-content: space-between; color: #7f8c8d; font-size: 14px;">
                            <span><i class="fas fa-eye"></i> 3,892 izlenme</span>
                            <span><i class="fas fa-heart"></i> 267 beğeni</span>
                        </div>
                    </div>
                </div>

                <!-- Load More Button -->
                <div class="text-center" style="margin-top: 30px;">
                    <button class="btn btn-outline-primary" style="padding: 10px 30px;">Daha Fazla Göster</button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Prayer Times Widget (Partial View) -->
                @Html.Partial("_PrayerTimesWidget")

                <!-- Popular Scholars Widget -->
                <div class="widget" style="background: white; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 2px 12px rgba(0,0,0,0.06); overflow: hidden; border: 1px solid rgba(0,0,0,0.04);">
                    <div class="widget-header" style="background: #9bb5a2; color: white; padding: 15px;">
                        <h6 style="margin: 0; font-weight: 500; font-family: 'Inter', sans-serif;">Takip Edilebilecek Hocalar</h6>
                    </div>
                    <div class="widget-content" style="padding: 20px;">
                        <div class="scholar-item" style="display: flex; align-items: center; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #ecf0f1;">
                            <div class="scholar-avatar" style="width: 40px; height: 40px; background: #8fa4b3; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                <i class="fas fa-user" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div style="flex: 1;">
                                <h6 style="margin: 0; color: #4a4a4a; font-size: 14px; font-family: 'Inter', sans-serif; font-weight: 500;">Yasin Karataş Hoca</h6>
                                <small style="color: #6b7280; font-size: 13px;">1,245 takipçi</small>
                            </div>
                            <button style="background: #8fa4b3; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; font-family: 'Inter', sans-serif; transition: background 0.3s;">Takip Et</button>
                        </div>
                        <div class="scholar-item" style="display: flex; align-items: center; margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #ecf0f1;">
                            <div class="scholar-avatar" style="width: 40px; height: 40px; background: #b89090; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                                <i class="fas fa-user" style="color: white; font-size: 16px;"></i>
                            </div>
                            <div style="flex: 1;">
                                <h6 style="margin: 0; color: #4a4a4a; font-size: 14px; font-family: 'Inter', sans-serif; font-weight: 500;">Örnek Hoca</h6>
                                <small style="color: #6b7280; font-size: 13px;">892 takipçi</small>
                            </div>
                            <button style="background: #8fa4b3; color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; font-family: 'Inter', sans-serif; transition: background 0.3s;">Takip Et</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.spotlight-card:hover {
    transform: translateY(-5px);
}

.content-card {
    transition: box-shadow 0.3s;
}

.content-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.play-btn:hover {
    background: #2980b9 !important;
}

#city-selector option {
    background: #2c3e50;
    color: white;
}

@@media (max-width: 768px) {
    .spotlight-card {
        margin-bottom: 20px;
    }

    .content-card {
        margin-bottom: 20px;
    }

    .widget {
        margin-bottom: 20px;
    }
}
</style>

@section scripts {
<script>
$(document).ready(function() {
    console.log('Sayfa hazır, namaz vakitleri başlatılıyor...');

    // Namaz vakitleri widget'ı başlat
    initPrayerTimesWidget();
});

function initPrayerTimesWidget() {
    console.log('Namaz vakitleri widget başlatılıyor...');

    // Sayfa yüklendiğinde namaz vakitlerini ve şehirleri getir
    loadPrayerTimes();
    loadCities();

    // Şehir değiştirildiğinde
    $('#city-selector').change(function() {
        var city = $(this).val();
        console.log('Şehir değişti:', city);
        if (city) {
            loadPrayerTimes(city);
            $('#prayer-city-name').text($(this).find('option:selected').text());
        }
    });
}

function loadPrayerTimes(city) {
    console.log('Namaz vakitleri yükleniyor, şehir:', city || 'varsayılan');

    $.ajax({
        url: '@Url.Action("GetPrayerTimes", "Home")',
        type: 'GET',
        data: city ? { city: city } : {},
        success: function(response) {
            console.log('API yanıtı:', response);

            try {
                // Response kontrolü ve data erişimi
                if (response && response.data && response.data.times) {
                    var data = response.data;
                    console.log('Veri:', data);

                    // Tarih güncelle
                    $('#prayer-date').text(data.date);

                    // Vakitleri güncelle
                    $('#imsak-time').text(data.times.imsak);
                    $('#gunes-time').text(data.times.gunes);
                    $('#ogle-time').text(data.times.ogle);
                    $('#ikindi-time').text(data.times.ikindi);
                    $('#aksam-time').text(data.times.aksam);
                    $('#yatsi-time').text(data.times.yatsi);

                    // Şehir adını güncelle
                    if (data.city) {
                        var cityName = data.city.charAt(0).toUpperCase() + data.city.slice(1);
                        $('#prayer-city-name').text(cityName);
                        console.log('Şehir adı güncellendi:', cityName);
                    }

                    console.log('Namaz vakitleri başarıyla güncellendi');
                } else {
                    console.error('Veri bulunamadı:', response);
                    $('#prayer-date').text('Veri Hatası');
                }
            } catch (e) {
                console.error('JavaScript hatası:', e);
                $('#prayer-date').text('JavaScript Hatası: ' + e.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX hatası:', status, error);
            $('#prayer-date').text('Bağlantı hatası: ' + error);
        }
    });
}

function loadCities() {
    console.log('Şehirler yükleniyor...');

    $.ajax({
        url: '@Url.Action("GetCities", "Home")',
        type: 'GET',
        success: function(response) {
            console.log('Şehirler API yanıtı:', response);

            try {
                // Response kontrolü ve cities erişimi
                if (response && response.data && Array.isArray(response.data)) {
                    var cities = response.data;
                    var selector = $('#city-selector');
                    selector.empty();
                    selector.append('<option value="">Şehir Seçin...</option>');

                    $.each(cities, function(index, city) {
                        selector.append('<option value="' + city.id + '">' + city.name + '</option>');
                    });

                    console.log('Şehirler başarıyla yüklendi:', cities.length, 'şehir');
                } else {
                    console.error('Şehirler verisi bulunamadı:', response);
                }
            } catch (e) {
                console.error('Şehirler JavaScript hatası:', e);
            }
        },
        error: function(xhr, status, error) {
            console.error('Şehirler AJAX hatası:', status, error);
        }
    });
}
</script>
}




