<!DOCTYPE html>
<html lang="@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName)" dir="@(System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName == "ar" ? "rtl" : "ltr")">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="@ViewBag.MetaDescription" />
    <meta name="keywords" content="@ViewBag.MetaKeywords" />
    <meta name="author" content="Yasin <PERSON>" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@ViewBag.Title" />
    <meta property="og:description" content="@ViewBag.MetaDescription" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="@Request.Url.AbsoluteUri" />
    <meta property="og:site_name" content="Yasin Karataş Hoca" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="@ViewBag.Title" />
    <meta name="twitter:description" content="@ViewBag.MetaDescription" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="@(Request.IsSecureConnection ? Request.Url.AbsoluteUri : Request.Url.AbsoluteUri.Replace("http://", "https://"))" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/Content/Site.css" rel="stylesheet" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header (İslamway tarzı) -->
    <header class="site-header" style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Top Bar -->
        <div class="top-bar" style="background: #2c3e50; color: white; padding: 8px 0; font-size: 14px;">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span><i class="fas fa-envelope"></i> <EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="header-right">
                            <!-- Language Selector -->
                            <div class="language-selector" style="display: inline-block; margin-right: 15px;">
                                <div class="dropdown">
                                    <button class="btn btn-sm dropdown-toggle" type="button" data-toggle="dropdown" style="background: none; border: 1px solid rgba(255,255,255,0.3); color: white; font-size: 12px;">
                                        @{
                                            var currentLang = System.Threading.Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;
                                            var langText = currentLang == "tr" ? "Türkçe" : currentLang == "en" ? "English" : "العربية";
                                        }
                                        <i class="fas fa-globe"></i> @langText
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a href="/Home/Index?language=tr">Türkçe</a></li>
                                        <li><a href="/Home/Index?language=en">English</a></li>
                                        <li><a href="/Home/Index?language=ar">العربية</a></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Login Button -->
                            <div style="display: inline-block;">
                                <a href="#" style="color: white; text-decoration: none; font-size: 12px;">
                                    <i class="fas fa-user"></i> Giriş Yap
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Header -->
        <div class="main-header" style="padding: 15px 0; background: white;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <!-- Logo (İslamway tarzı) -->
                        <div class="site-logo">
                            <a href="/" style="text-decoration: none;">
                                <img src="/Content/images/logo.png" alt="Yasin Karataş Hoca" style="height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <h2 class="logo-text" style="color: #2c3e50; font-size: 24px; font-weight: 700; margin: 0; display: none;">Yasin Karataş</h2>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- Search Box (İslamway tarzı) -->
                        <div class="search-box">
                            <form class="search-form" method="get" action="/Home/Search">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="q" placeholder="Ara..." style="border: 2px solid #ecf0f1; border-radius: 25px 0 0 25px; padding: 10px 15px; font-size: 14px;" />
                                    <div class="input-group-btn">
                                        <button class="btn btn-search" type="submit" style="background: #3498db; color: white; border: 2px solid #3498db; border-radius: 0 25px 25px 0; padding: 10px 20px;">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-3 text-right">
                        <!-- Quick Actions -->
                        <div class="header-actions">
                            <a href="#" style="color: #7f8c8d; margin-right: 15px; text-decoration: none;">
                                <i class="fas fa-heart"></i>
                            </a>
                            <a href="#" style="color: #7f8c8d; margin-right: 15px; text-decoration: none;">
                                <i class="fas fa-bookmark"></i>
                            </a>
                            <a href="#" style="color: #7f8c8d; text-decoration: none;">
                                <i class="fas fa-bell"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation (İslamway tarzı) -->
        <nav class="main-navigation" style="background: #5a6c7d; border-top: 3px solid #8fa4b3;">
            <div class="container">
                <div class="navbar navbar-default" style="background: none; border: none; margin: 0;">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse" style="border: 1px solid rgba(255,255,255,0.3);">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar" style="background: white;"></span>
                            <span class="icon-bar" style="background: white;"></span>
                            <span class="icon-bar" style="background: white;"></span>
                        </button>
                    </div>
                    <div class="collapse navbar-collapse" id="navbar-collapse">
                        <ul class="nav navbar-nav" style="margin: 0;">
                            <li>
                                <a href="/" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-home"></i> Ana Sayfa
                                </a>
                            </li>
                            <li>
                                <a href="/Home/About" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-user"></i> Hakkında
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Quran" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-book-open"></i> Kur'an
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Lessons" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-graduation-cap"></i> Dersler
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Videos" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-video"></i> Videolar
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Articles" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-newspaper"></i> Makaleler
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Books" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-book"></i> Kitaplar
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Fatawa" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-question-circle"></i> Fetva
                                </a>
                            </li>
                            <li>
                                <a href="/Home/Contact" style="color: white; padding: 15px 20px; display: block; text-decoration: none; transition: background 0.3s;">
                                    <i class="fas fa-envelope"></i> İletişim
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>Yasin Karataş Hoca</h5>
                        <p>İslami ilimler alanında eğitim ve öğretim faaliyetleri.</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>Hızlı Linkler</h5>
                        <ul class="footer-links">
                            <li><a href="/">Ana Sayfa</a></li>
                            <li><a href="/Home/About">Hakkında</a></li>
                            <li><a href="/Home/Lessons">Dersler</a></li>
                            <li><a href="/Home/Articles">Makaleler</a></li>
                            <li><a href="/Home/Contact">İletişim</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="footer-section">
                        <h5>İletişim</h5>
                        <div class="contact-info">
                            <p><i class="fas fa-envelope"></i> <EMAIL></p>
                            <p><i class="fas fa-globe"></i> www.yasinkaratas.com.tr</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <p>&copy; @DateTime.Now.Year Yasin Karataş Hoca. Tüm hakları saklıdır. (Ver. 1.0.1)</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js"></script>
    @RenderSection("scripts", required: false)
    
    <script>
        // Prayer times clock (placeholder)
        function updatePrayerClock() {
            var now = new Date();
            var timeString = now.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
            document.getElementById('prayer-clock').textContent = timeString;
        }
        
        setInterval(updatePrayerClock, 1000);
        updatePrayerClock();
    </script>
</body>
</html>
